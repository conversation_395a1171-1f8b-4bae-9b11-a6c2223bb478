# Template angular-deploy
# This template allows you to build and deploy your Angular app to an S3 bucket.
image: atlassian/default-image:4

clone:
  depth: full

definitions:
  build_and_deploy: &build_and_deploy
    name: Build and Deploy Angular App
    oidc: true
    script:
      - npm install -g @angular/cli
      - npm install
      - ng build --configuration production
      - export AWS_MAIN_ACCOUNT_ID="************" ###MARK_MAIN_ACCOUNT
      - export AWS_WEB_IDENTITY_TOKEN_FILE="/tmp/aws-web-identity-token-file"
      - export AWS_ROLE_ARN="arn:aws:iam::${AWS_MAIN_ACCOUNT_ID}:role/bitbucket-openid"
      - echo ${BITBUCKET_STEP_OIDC_TOKEN} > ${AWS_WEB_IDENTITY_TOKEN_FILE}
      - test -f dotenv && source dotenv
      - curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
      - unzip awscliv2.zip
      - ./aws/install
      - echo "using ${EXECUTE_ROLE_ARN} to  run deployment"
      - eval $(aws sts assume-role --role-arn ${EXECUTE_ROLE_ARN} --role-session-name angular-deploy | jq -r '.Credentials | "export AWS_ACCESS_KEY_ID=\(.AccessKeyId)\nexport AWS_SECRET_ACCESS_KEY=\(.SecretAccessKey)\nexport AWS_SESSION_TOKEN=\(.SessionToken)\n"')
      - mkdir -p dist/$APP_DIST
      - unset AWS_WEB_IDENTITY_TOKEN_FILE AWS_ROLE_ARN
      - aws s3 sync dist/$APP_DIST/ s3://${S3_BUCKET} --delete
    services:
      - docker

pipelines:
  custom:
    deploy_angular_web:
      - step:
          name: Generate deployment variables
          artifacts:
            - dotenv
          script:
            - echo "export S3_BUCKET=web-bcochile-dev-555456986164" > dotenv
            - echo "export DISTRIBUTION_ID=E2L4HYYXHP1NKM" >> dotenv
            - echo "export EXECUTE_ROLE_ARN=arn:aws:iam::555456986164:role/deployer" >> dotenv
            - echo "export APP_DIST=web-enterprise-bco" >> dotenv
      - step: *build_and_deploy
