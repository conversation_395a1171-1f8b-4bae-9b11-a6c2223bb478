{"name": "web-enterprise-bco", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test"}, "private": true, "dependencies": {"@angular/animations": "~13.3.0", "@angular/cdk": "^13.0.2", "@angular/common": "~13.3.0", "@angular/compiler": "~13.3.0", "@angular/core": "~13.3.0", "@angular/forms": "~13.3.0", "@angular/localize": "~13.3.0", "@angular/platform-browser": "~13.3.0", "@angular/platform-browser-dynamic": "~13.3.0", "@angular/router": "~13.3.0", "@ng-bootstrap/ng-bootstrap": "^12.1.2", "@popperjs/core": "^2.10.2", "bootstrap": "^5.1.3", "file-saver": "^2.0.5", "js-sha256": "^0.9.0", "ngx-spinner": "^13.1.1", "primeicons": "^5.0.0", "primeng": "^13.0.0", "rxjs": "~7.5.0", "sweetalert2": "^11.4.29", "tslib": "^2.3.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "~13.3.2", "@angular/cli": "~13.3.2", "@angular/compiler-cli": "~13.3.0", "@types/file-saver": "^2.0.5", "@types/jasmine": "~3.10.0", "@types/node": "^12.20.55", "jasmine-core": "~4.0.0", "karma": "~6.3.0", "karma-chrome-launcher": "~3.1.0", "karma-coverage": "~2.1.0", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "~1.7.0", "typescript": "~4.6.2"}}