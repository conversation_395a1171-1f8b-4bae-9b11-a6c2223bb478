/* You can add global styles to this file, and also import other style files */
/* You can add global styles to this file, and also import other style files */
html, body{
  padding: 0;
  margin: 0;
}

html, body{
  min-height: 100vh !important;
  height: 100vh;
}
.min-vh-component{
  height: 100vh;
}
.min-vh-component-access{
  height: 85vh;
}
.menu-vertical-top{
  margin-top: 55px !important;
}
.content-page-top-margin{
  margin-top: 63px !important;
}
.bg-gris-mobid{
  background-color: #585858;
}
.style-bg-menu{
  background-color: #585858;
  border: 0;
  color: aliceblue;
}
.p-megamenu .p-megamenu-root-list > .p-menuitem{
  background-color: #777;
}
.p-megamenu .p-megamenu-root-list > .p-menuitem > .p-menuitem-link .p-menuitem-text {
  color:#fff;
}
.p-megamenu .p-megamenu-root-list > .p-menuitem > .p-menuitem-link .p-menuitem-icon {
  color:#fff;
}
.p-megamenu .p-megamenu-root-list > .p-menuitem > .p-menuitem-link .p-submenu-icon {
  color:#fff;
}

.p-tieredmenu{
  border-radius: 0;
}
.p-tieredmenu .p-submenu-list{
  background-color: #777;
}
.p-tieredmenu .p-menuitem > .p-menuitem-link .p-menuitem-text {
  color:#fff;
}
.p-tieredmenu .p-menuitem > .p-menuitem-link .p-menuitem-icon {
  color:#fff;
}
.p-tieredmenu .p-menuitem > .p-menuitem-link .p-submenu-icon {
  color:#fff;
}
#imagenLogin{
  position:absolute;
  z-index:-1;
  height: 100%;
  width: 70%;
}
#imagenHome{
  position:absolute;
  z-index:-1;
  height: 100%;
  width: 75%;
}

.error {
  position: relative;
  z-index: 1;
  padding: 7px;
  border-radius: 10px;
  color: white;
  width: 100%;
  text-align: center;
  font-size: 12px;
  background: RGB(194, 46, 79);
}

.p-button-label{
  font-weight: normal;
}

.p-inputtext{
  padding: 0.4rem 0.4rem;
}

#phoneNumberUsuario{
  width: 100% !important;
}

.p-button.p-button-icon-only{
  padding: 0.4rem 0;
}

.h100{
  height: 100vh;
}

/* Importing Bootstrap SCSS file. */
@import '~bootstrap/scss/bootstrap';
