import { AuthPagesComponent } from './modules/auth/pages/auth-pages/auth-pages.component';
import { SessionGuard } from './core/guards/session.guard';
import { HomePageComponent } from './modules/home/<USER>/home-page/home-page.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

const routes: Routes = [
  {
    path:'auth',
    component:AuthPagesComponent,
    loadChildren:() => import('./modules/auth/auth.module').then(m=>m.AuthModule)
  },
  {
    path:'home',
    component:HomePageComponent,
    loadChildren:() => import('./modules/home/<USER>').then(m=>m.HomeModule),
    canActivate:[SessionGuard]
  },
  { path: '', redirectTo: 'auth', pathMatch: 'full' }

];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule { }
