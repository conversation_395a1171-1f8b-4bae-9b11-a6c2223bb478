import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { MaintenanceRoutingModule } from './maintenance-routing.module';
import { MaintenancePageComponent } from './pages/maintenance-page/maintenance-page.component';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { InjetTokenInterceptor } from '@core/interceptors/injet-token.interceptor';
import { ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '@shared/shared.module';
import {InputMaskModule} from 'primeng/inputmask';


@NgModule({
  declarations: [
    MaintenancePageComponent
  ],
  imports: [
    CommonModule,
    MaintenanceRoutingModule,
    ReactiveFormsModule,
    SharedModule,
    InputMaskModule
  ]
})
export class MaintenanceModule { }
