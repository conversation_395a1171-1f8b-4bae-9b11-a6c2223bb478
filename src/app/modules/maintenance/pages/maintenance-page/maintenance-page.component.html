<div class="container-fluid bg-white h100">
  <h4 class="pt-3">Listado de usuarios</h4>
  <form class="row">
    <div class="form-group form-inline col-12 col-md-8">
      Buscar: <input class="form-control" type="text" [formControl]="filter" />
    </div>
    <div class="form-group form-inline col-6 col-md-2 pb-3">
      <button type="button" ngbAutofocus class="form-control mt-4 btn btn-primary"
        (click)="openModalNuevo(contenido)">Nuevo
        Usuario</button>
    </div>
    <div class="form-group form-inline col-6 col-md-2 pb-3">
      <button type="button" ngbAutofocus class="form-control mt-4 btn btn-primary" (click)="generarReporte()">Exportar Excel</button>
    </div>
  </form>
  <div  style="height: 70%; overflow: scroll;">
    <table class="table table-striped osdo" aria-label="">
      <thead>
        <tr>
          <th scope="col">#</th>
          <th scope="col">Usuario</th>
          <th scope="col">Rut</th>
          <th scope="col">Nombres</th>
          <th scope="col">Apellidos</th>
          <th scope="col">Email</th>
          <th scope="col">Teléfono</th>
          <th scope="col">Unidad</th>
          <th scope="col">Último Ingreso</th>
          <th scope="col">Cambio Password</th>
          <th scope="col">Fecha Creación</th>
          <th scope="col">Estado</th>
          <th scope="col">Perfil</th>
          <th scope="col" class="text-center">Editar</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let usuario of usuarios$ | async; index as i">
          <th scope="row">{{ i + 1 }}</th>
          <td>{{ usuario.username }}</td>
          <td>{{ usuario.rut |FormatRut}}</td>
          <td>{{ usuario.name|titlecase}}</td>
          <td>{{ usuario.lastName|titlecase}}</td>
          <td>{{ usuario.mail}}</td>
          <td>{{ usuario.phoneNumber}}</td>
          <td>{{ usuario.unit |titlecase}}</td>
          <td>{{ usuario.lastAccess |date:'YYY-MM-dd HH:mm:ss'}}</td>
          <td>{{ usuario.lastChangePassword|date:'YYYY-MM-dd'}}</td>
          <td>{{ usuario.createDate |date:'YYYY-MM-dd'}}</td>
          <td *ngIf="usuario.enabled;else inactivo">Activo</td>
          <ng-template #inactivo>
            <td>Inactivo</td>
          </ng-template>
          <td>{{ usuario.profile.description|titlecase}}</td>
          <td class="text-center">
            <button on class="btn btn-info btn-circle text-white btn-sm" (click)="cargarDatos(usuario,contenido)"><em
                class="pi pi-user-edit"></em></button>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <ng-template #contenido let-modal>
    <form [formGroup]="editSaveForm" (ngSubmit)="sendData()">
      <div class="modal-header">
        <h4 *ngIf="!editState" class="modal-title">Nuevo Usuario</h4>
        <h4 *ngIf="editState" class="modal-title">Editar Usuario</h4>
        <button class="close" aria-label="close" type="button" (click)="modal.dismiss()">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body">
        <div class="container row">
          <div class="col-md-6">
            <div class="mb-3">
              <label for="validationCustomUsername">Nombre de Usuario</label>
              <div class="input-group">
                <input type="text" class="form-control" formControlName="nombreUsuario" id="validationCustomUsername"
                  placeholder="Nombre Usuario" aria-describedby="inputGroupPrepend">
              </div>
              <div
                *ngIf="editSaveForm.controls['nombreUsuario'].invalid && (editSaveForm.controls['nombreUsuario'].dirty || editSaveForm.controls['nombreUsuario'].touched)"
                class="error mt-1">
                <div *ngFor="let validation of smsValidaciones.nombreUsuario">
                  <span
                    *ngIf="editSaveForm.controls['nombreUsuario'].hasError(validation.type) && (editSaveForm.controls['nombreUsuario'].dirty || editSaveForm.controls['nombreUsuario'].touched)">{{validation.message}}</span>
                </div>
              </div>
            </div>
            <div class="mb-3">
              <label for="validationCustom01">Rut</label>
              <input type="text" class="form-control" formControlName="rut" id="validationCustom01"
                placeholder="Ingresar rut sin guiones">
              <div
                *ngIf="editSaveForm.controls['rut'].invalid && (editSaveForm.controls['rut'].dirty || editSaveForm.controls['rut'].touched)"
                class="error mt-1">
                <div *ngFor="let validation of smsValidaciones.rut">
                  <span
                    *ngIf="editSaveForm.controls['rut'].hasError(validation.type) && (editSaveForm.controls['rut'].dirty || editSaveForm.controls['rut'].touched)">{{validation.message}}</span>
                </div>
              </div>
            </div>
            <div class="mb-3">
              <label for="validationCustom01">Nombres</label>
              <input type="text" class="form-control" formControlName="nombres" id="validationCustom01"
                placeholder="Nombres">
              <div
                *ngIf="editSaveForm.controls['nombres'].invalid && (editSaveForm.controls['nombres'].dirty || editSaveForm.controls['nombres'].touched)"
                class="error mt-1">
                <div *ngFor="let validation of smsValidaciones.nombres">
                  <span
                    *ngIf="editSaveForm.controls['nombres'].hasError(validation.type) && (editSaveForm.controls['nombres'].dirty || editSaveForm.controls['nombres'].touched)">{{validation.message}}</span>
                </div>
              </div>
            </div>
            <div class="mb-3">
              <label for="validationCustom02">Apellidos</label>
              <input type="text" class="form-control" formControlName="apellidos" id="validationCustom02"
                placeholder="Apellidos">
              <div
                *ngIf="editSaveForm.controls['apellidos'].invalid && (editSaveForm.controls['apellidos'].dirty || editSaveForm.controls['apellidos'].touched)"
                class="error mt-1">
                <div *ngFor="let validation of smsValidaciones.apellidos">
                  <span
                    *ngIf="editSaveForm.controls['apellidos'].hasError(validation.type) && (editSaveForm.controls['apellidos'].dirty || editSaveForm.controls['apellidos'].touched)">{{validation.message}}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="col-md-6">

            <div class="mb-3">
              <label for="validationCustom03">Correo Electronico</label>
              <input type="text" class="form-control" formControlName="email" id="validationCustom03"
                placeholder="Correo electronico">
              <div
                *ngIf="editSaveForm.controls['email'].invalid && (editSaveForm.controls['email'].dirty || editSaveForm.controls['email'].touched)"
                class="error mt-1">
                <div *ngFor="let validation of smsValidaciones.email">
                  <span
                    *ngIf="editSaveForm.controls['email'].hasError(validation.type) && (editSaveForm.controls['email'].dirty || editSaveForm.controls['email'].touched)">{{validation.message}}</span>
                </div>
              </div>
            </div>
            <div class="mb-3">
              <label for="validationCustom04">Teléfono</label>
              <div class="p-col-12" >
                <p-inputMask  inputId="phoneNumberUsuario"  mask="+(56) 9 9999-99-99"  formControlName="telefono" placeholder="Teléfono">
                </p-inputMask>
              </div>
              <div
                *ngIf="editSaveForm.controls['telefono'].invalid && (editSaveForm.controls['telefono'].dirty || editSaveForm.controls['telefono'].touched)"
                class="error mt-1">
                <div *ngFor="let validation of smsValidaciones.telefono">
                  <span
                    *ngIf="editSaveForm.controls['telefono'].hasError(validation.type) && (editSaveForm.controls['telefono'].dirty || editSaveForm.controls['telefono'].touched)">{{validation.message}}</span>
                </div>
              </div>
            </div>
            <div class="mb-3">
              <label for="validationCustom05">Unidad</label>
              <input type="text" class="form-control" formControlName="unidad" id="validationCustom05"
                placeholder="Unidad">
              <div
                *ngIf="editSaveForm.controls['unidad'].invalid && (editSaveForm.controls['unidad'].dirty || editSaveForm.controls['unidad'].touched)"
                class="error mt-1">
                <div *ngFor="let validation of smsValidaciones.unidad">
                  <span
                    *ngIf="editSaveForm.controls['unidad'].hasError(validation.type) && (editSaveForm.controls['unidad'].dirty || editSaveForm.controls['unidad'].touched)">{{validation.message}}</span>
                </div>
              </div>
            </div>
            <div class="mb-3">
              <label for="">Perfil</label>
              <select formControlName="perfil"  (change)="selectProfile($event.target)" class="form-select">
                <option value="2">Admin</option>
                <option value="7">Viewer</option>
                <option value="8">Maintenance</option>
                <option value="9">Audit</option>
              </select>
              <div
                *ngIf="editSaveForm.controls['perfil'].invalid && (editSaveForm.controls['perfil'].dirty || editSaveForm.controls['perfil'].touched)"
                class="error mt-1">
                <div *ngFor="let validation of smsValidaciones.perfil">
                  <span
                    *ngIf="editSaveForm.controls['perfil'].hasError(validation.type) && (editSaveForm.controls['perfil'].dirty || editSaveForm.controls['perfil'].touched)">{{validation.message}}</span>
                </div>
              </div>
            </div>
            <div class="mb-3">
              <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" formControlName='estado' id="inlineRadio1" value=true>
                <label class="form-check-label" for="inlineRadio1">Activo</label>
              </div>
              <div class="form-check form-check-inline">
                <input class="form-check-input" type="radio" formControlName='estado' id="inlineRadio2" value=false>
                <label class="form-check-label" for="inlineRadio2">Inactivo</label>
              </div>
              <div
                *ngIf="editSaveForm.controls['estado'].invalid && (editSaveForm.controls['estado'].dirty || editSaveForm.controls['estado'].touched)"
                class="error mt-1">
                <div *ngFor="let validation of smsValidaciones.estado">
                  <span
                    *ngIf="editSaveForm.controls['estado'].hasError(validation.type) && (editSaveForm.controls['estado'].dirty || editSaveForm.controls['estado'].touched)">{{validation.message}}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-danger" (click)="modal.close()">Cancelar</button>
        <button type='submit' [disabled]="!editSaveForm.valid" *ngIf="!editState"
          class="btn btn-success">Guardar</button>
        <button type='submit' [disabled]="!editSaveForm.valid" *ngIf="editState"
          class="btn btn-success">Guardar</button>
      </div>

    </form>
  </ng-template>





</div>
