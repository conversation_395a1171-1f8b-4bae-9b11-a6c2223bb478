import { SharedService } from './../../../../shared/services/shared.service';
import { AuthService } from './../../../auth/services/auth.service';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { UserDto } from '@core/models/user/user.model';
import { UserService } from '@modules/maintenance/services/user.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { map, Observable, startWith, Subscription } from 'rxjs';
import Swal from 'sweetalert2';
import * as FileSaver from 'file-saver';

@Component({
  selector: 'app-maintenance-page',
  templateUrl: './maintenance-page.component.html',
  styleUrls: ['./maintenance-page.component.scss']
})
export class MaintenancePageComponent implements OnInit {
  editSaveForm:FormGroup= new FormGroup({});

  usuarios$!: Observable<UserDto[]>;
  filter = new FormControl('');
  editState: boolean = false;
  bodyData: any = [];
  txtMessage: string = "";
  nuevoUsuario: any;
  emailPattern = "^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z0-9.-]{2,4}$";
  ip:string|null;

  datosReporte: any = {
    "page": 0,
    "size": 200,
    "dataFilter": "",
    "user": {
      "company": {
        "id": 0
      },
      "profile": {
        "id": 0
      }
    },
    "ip":"",
    "entity":""
  }
  idUsuarioActualizar: any;
  smsValidaciones: any;
  suscription!: Subscription;
  valor: any;
  entity: any;
  descripcionPerfil:any;

  constructor(
    public modal: NgbModal,
    private _usuarioService: UserService,
    private _authService:AuthService,
    private _sharedService:SharedService) {
      this.ip = sessionStorage.getItem('ip');
      this.datosReporte.ip=this.ip;
      this.datosReporte.entity=0;

      this._usuarioService.getAllUser(this.datosReporte)
      .subscribe((obj: any) => {
        this.bodyData = obj.data;
        this.usuarios$ = this.filter.valueChanges.pipe(
          startWith(''),
          map(text => this.search(text))
        );
      },
        (errorData: any) => {
          this.txtMessage = errorData.error;
          console.log(errorData);
        }
      );

      this.smsValidaciones = {
        nombreUsuario: [
          { type: 'required', message: 'Se te ha olvidado ingresar el nombre de usuario' },
          { type: 'minlength', message: 'Recuerda digitar minimo 5 carácteres' },
          { type: 'maxlength', message: 'Recuerda digitar un maximo 36 carácteres' },
          { type: 'pattern', message: 'El nombre de usuario no debe contener espacios ni carácteres especiales' }

        ],
        nombres: [
          { type: 'required', message: 'Recuerda ingresar los nombres' },
          { type: 'minlength', message: 'Recuerda digitar minimo 4 carácteres' },
          { type: 'maxlength', message: 'Recuerda digitar un maximo 36 carácteres' },
          { type: 'pattern', message: 'Recuerda que debe tener minimo un numero,una minuscula y una mayuscula,sin carácteres especiales o espacios' }
        ],
        apellidos: [
          { type: 'required', message: 'Recuerda ingresar los apellidos' },
          { type: 'minlength', message: 'Recuerda digitar minimo 4 carácteres' },
          { type: 'maxlength', message: 'Recuerda digitar un maximo 36 carácteres' },
          { type: 'pattern', message: 'Recuerda que debe tener minimo un numero,una minuscula y una mayuscula,sin carácteres especiales o espacios' }
        ],
        email: [
          { type: 'required', message: 'Recuerda ingresar el Correo Electronico' },
          { type: 'minlength', message: 'Recuerda digitar minimo 8 carácteres' },
          { type: 'maxlength', message: 'Recuerda digitar un maximo 36 carácteres' },
          { type: 'pattern', message: 'Formato de Email incorrecto' }
        ],
        telefono: [
          { type: 'minlength', message: 'Recuerda digitar minimo 9 carácteres' },
          { type: 'maxlength', message: 'Recuerda digitar un maximo 11 carácteres' },
          { type: 'pattern', message:   'Formato No valido' }
        ],
        unidad: [
          { type: 'minlength', message: 'Recuerda digitar minimo 3 carácteres' },
          { type: 'maxlength', message: 'Recuerda digitar un maximo 36 carácteres' }
        ],
        perfil: [
          { type: 'required', message: 'Recuerda seleccionar un perfil' },
          { type: 'minlength', message: 'Recuerda digitar minimo 3 carácteres' },
          { type: 'maxlength', message: 'Recuerda digitar un maximo 36 carácteres' }
        ],
        rut: [
          { type: 'required', message: 'Recuerda ingresar el rut' },
          { type: 'minlength', message: 'Recuerda digitar minimo 7 carácteres' },
          { type: 'maxlength', message: 'Recuerda digitar un maximo 9 carácteres' }
        ],
        estado: [
          { type: 'required', message: 'Recuerda ingresar el nombre' },
          { type: 'minlength', message: 'Recuerda digitar minimo 3 carácteres' },
          { type: 'maxlength', message: 'Recuerda digitar un maximo 36 carácteres' }
        ]
      };
     }

  ngOnInit(): void {
    this.editSaveForm = new FormGroup({
      nombreUsuario: new FormControl('', [Validators.required, Validators.minLength(5), Validators.maxLength(36)]),
      nombres: new FormControl('', [Validators.required, Validators.minLength(4), Validators.maxLength(36)]),
      apellidos: new FormControl('', [Validators.required, Validators.minLength(4), Validators.maxLength(36)]),
      email: new FormControl('', [Validators.required, Validators.minLength(8), Validators.maxLength(36), Validators.pattern(this.emailPattern)]),
      telefono: new FormControl('', [ Validators.minLength(8),Validators.maxLength(18)]),
      unidad: new FormControl('', [Validators.maxLength(36)]),
      rut: new FormControl('', [Validators.required,Validators.minLength(7),Validators.maxLength(13)]),
      perfil: new FormControl('', [Validators.required]),
      estado: new FormControl('true', [Validators.required])
    });
  }

  openModalNuevo(contenido: any) {
    this.editSaveForm.reset();
    this.modal.open(contenido, { size: 'lg' });
    this.editState = false;
    this.editSaveForm.controls['estado'].setValue("true");
    this.editSaveForm.controls['nombreUsuario'].enable();
  }

  search(text: string): UserDto[] {
    return this.bodyData.filter((usuario: UserDto) => {
      const term = text.toLowerCase();
      return usuario.username.toLowerCase().includes(term) ||
        usuario.name.toLowerCase().includes(term) ||
        usuario.lastName.toLowerCase().includes(term);
    });
  }

  selectProfile(event:any):void{
    this.descripcionPerfil = event.selectedOptions[0].innerText;
  }

  cargarDatos(datos: any, contenido: any) {
    this.modal.open(contenido, { size: 'lg' });

    this.idUsuarioActualizar = datos.id;
    this.editSaveForm.controls['nombreUsuario'].setValue(datos.username);
    this.editSaveForm.controls['rut'].setValue(this._sharedService.format(datos.rut));
    this.editSaveForm.controls['nombres'].setValue(datos.name);
    this.editSaveForm.controls['apellidos'].setValue(datos.lastName);
    this.editSaveForm.controls['email'].setValue(datos.mail);
    this.editSaveForm.controls['telefono'].setValue(datos.phoneNumber);
    this.editSaveForm.controls['unidad'].setValue(datos.unit);
    this.editSaveForm.controls['perfil'].setValue(datos.profile.id);
    this.editSaveForm.controls['estado'].setValue(datos.enabled + "");

    this.editState = true;
    this.editSaveForm.controls['nombreUsuario'].disable();
  }

  loadDataTable() {
    this._usuarioService.getAllUser(this.datosReporte)
       .subscribe(
         (obj: any) => {
           this.bodyData = obj.data;
           this.usuarios$ = this.filter.valueChanges.pipe(
             startWith(''),
             map(text => this.search(text))
           );
         },
         (errorData: any) => {
           this.txtMessage = errorData.error;
           console.log(errorData);
         }
       );
   }

  saveUser():void {
    this.jsonUsuario();
    const user = this.editSaveForm.value;

    this._usuarioService.saveUser(this.nuevoUsuario)
      .subscribe((obj: any) => {
        if (obj.code == 0) {
          this.modal.dismissAll();
          this._authService.forgotPassword(user.nombreUsuario, user.email).subscribe(
            (obj:any)=>{console.log("Se envio correo",obj)}
          );
          Swal.fire(obj.message, "Se enviara un correo a: " + user.email + " con su contraseña temporal", 'success');
        } else {
          Swal.fire('Ha ocurrido Algo', obj.message, 'error');
        }
        this.loadDataTable();
      },
        (errorData: any) => {
          this.txtMessage = errorData.error;
          console.log(errorData);
        }
      );
  }

  updateUser():void {
    this.editSaveForm.controls['nombreUsuario'].enable();
    this.jsonUsuario();

    this.editSaveForm.controls['nombreUsuario'].disable();
    this._usuarioService.updateUser(this.nuevoUsuario)
      .subscribe(
        (obj: any) => {
          if (obj.code == 0) {
            Swal.fire('Exito', obj.message, 'success');
            // var nm=user.nombreUsuario;
            // this.filter.setValue(nm.substring(3,-1));
            this.modal.dismissAll();
            this.loadDataTable();

          } else {
            Swal.fire('Ha ocurrido Algo', obj.message, 'error');
          }
        },
        (errorData: any) => {
          this.txtMessage = errorData.error;
          console.log(errorData);
        }
      );
  }

  sendData():void {
    const {nombreUsuario,nombres,apellidos,telefono,unidad,rut} = this.editSaveForm.value

    if (this._sharedService.onlyWordsNumbers(nombreUsuario)) {
      Swal.fire('Ocurrio Algo', 'Nombre usuario solo se permiten letras y números', 'error');

    }else if (this._sharedService.checkRut(rut)) {
      Swal.fire('Ocurrio Algo', 'Formato rut invalido', 'error');
    }
     else if (this._sharedService.onlyWordsNumberSpace(nombres)) {
      Swal.fire('Ocurrio Algo', 'Los nombres no llevan caracteres especiales ', 'error');

    } else if (this._sharedService.onlyWordsNumberSpace(apellidos)) {
      Swal.fire('Ocurrio Algo', 'Los apellidos no llevan caracteres especiales ', 'error');

    } else if (this._sharedService.onlyWordsNumberSpace(unidad)) {
      Swal.fire('Ocurrio Algo', 'El nombre de la unidad no puede contener caracteres especiales', 'error');

    } else {
      if (this.editState) {
        this.updateUser();
      } else {
        this.saveUser();
      }
    }
  }
  generarReporte():void{
    this.datosReporte.ip=this.ip;
      this.datosReporte.entity=1;
    this._usuarioService.getAllUser(this.datosReporte)
      .subscribe(
        (obj: any) => {
          let headerSpanish=['Usuario','Rut','Nombres','Apellidos','Email','Teléfono','Unidad','Ultimo Ingreso','Cambio Password','Fecha Creacion','Estado','Perfil',];
          let headerJson=['username','rut','name','lastName','mail','phoneNumber','unit','lastAccess','lastChangePassword','createDate','enabled','profiles']

         let data = this._sharedService.ConvertToCSV(obj.data,headerJson,headerSpanish);

          const blob = new Blob([data], { type: 'csv' });
          const csv = window.URL.createObjectURL(blob);

            FileSaver.saveAs(csv, 'Listado_usuarios.csv');
            console.log(data);
        },
        (errorData: any) => {
          this.txtMessage = errorData.error;
          console.log(errorData);
        }
      );
  }

  jsonUsuario(){
    const user = this.editSaveForm.value;
    this.nuevoUsuario = {
      "id": this.editState?this.idUsuarioActualizar:0,
      "idTimeZone": 5,
      "username": user.nombreUsuario,
      "password": "123456",
      "oldPassword": null,
      "mail": user.email,
      "rut":this._sharedService.clean(user.rut),
      "enabled": user.estado,
      "lastChangePassword": null,
      "name": user.nombres,
      "lastName": user.apellidos,
      "phoneNumber": this._sharedService.clean(user.telefono),
      "unit": user.unidad,
      "lastAccess": null,
      "ip":this.ip,
      "profile": {
        "id": user.perfil,
        "description": this.descripcionPerfil,
        "active": true
      },
      "company": {
        "id": 1,
        "idTimeZone": 0,
        "description": null,
        "maxTotalMsgs": 0,
        "nombreCorto": null
      }
    }
  }

}
