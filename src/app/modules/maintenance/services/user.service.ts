import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ResponseListUserDto } from '@core/models/report/responseListUserDto.model';
import { UserDto } from '@core/models/user/user.model';
import { UserData } from '@core/models/user/userData.model';
import { TokenService } from '@modules/auth/services/token.service';
import { Observable,Subject,tap } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private readonly URL =environment.urlAccountService;

  private _refresh$ = new Subject<void>();

  constructor(
    private http:HttpClient,
    private _tokenService:TokenService) { }

  getOneUser(usuario:string):Observable<UserData>{
    const TOKEN=this._tokenService.getToken()+"";
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
     return this.http.get<any>(`${this.URL}/api/user/get/`+usuario,options);
  }

  getAllUser(datos: any):Observable<UserDto> {
    const TOKEN = this._tokenService.getToken()+"";
    const BODY=datos;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
      headers = headers.set('Authorization', TOKEN);
    let options = { headers: headers };
    return this.http.post<UserDto>(`${this.URL}/api/user/list/maintener`,BODY,options);
  }

  saveUser(user: UserDto):Observable<any> {
    const TOKEN = this._tokenService.getToken()+"";
    const BODY=user;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Authorization', TOKEN);
    let options = { headers: headers };
    return this.http.post<UserDto>(`${this.URL}/api/user/insert`,BODY,options);
  }

  updateUser(user: UserDto):Observable<any> {
    const TOKEN = this._tokenService.getToken()+"";
    const BODY = user;
    let headers = new HttpHeaders();
    headers = headers.set('Content-Type', 'application/json');
    headers = headers.set('Authorization',TOKEN);
    let options = { headers: headers };
    return this.http.post<UserDto>(`${this.URL}/api/user/update`,BODY,options);
  }

  public getDataListUser(idCompany: number):Observable<ResponseListUserDto>{
    const TOKEN=this._tokenService.getToken()+"";
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
     return this.http.get<ResponseListUserDto>(`${this.URL}/api/user/list/`+idCompany,options);
  }

  get refresh$(){
    return this._refresh$;
  }
}
