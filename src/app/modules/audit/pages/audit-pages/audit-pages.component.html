<div class="bg-white h100">
  <app-header-report></app-header-report>

  <div *ngIf="totalRecords" class="card mt-3">
    <div class="row">
      <div class="offset-md-6 col-6 text-end">
        <button *ngIf="totalRecords!=0" (click)="generateReportExcelFull()" class="btn btn-primary m-1">Generar
          Reporte Completo</button>
      </div>
    </div>
    <div class="row">
      <div class="col-11  " [ngClass]="{ 'invisible': totalRecords == 0, 'visible': totalRecords != 0}">
        <div class="col-11">
          <p-paginator [rows]="rowsPage" [totalRecords]="totalRecords" [rowsPerPageOptions]="[10,20,30]"
            (onPageChange)="onPageChange($event)"></p-paginator>
        </div>
        <div class="col-12 my-2 text-center">{{txtMessageDataView}}</div>
      </div>
    </div>
    <div class="row">
      <div class="col-12 my-2">
        <div class="col-12 text-center">{{txtMessage}}</div>
      </div>
    </div>
    <div class="row">
      <div class="col-12 table-responsive">
        <table class="table table-striped" aria-label="">
          <thead id="headReport">
            <tr>
              <th scope="col">#</th>
              <th scope="col">Usuario</th>
              <th scope="col">Acción</th>
              <th scope="col">Descripción</th>
              <th scope="col">Fecha Accion</th>
              <th scope="col">Visualizar</th>
            </tr>
          </thead>
          <tbody id="bodyReport">
            <tr *ngFor="let usuario of bodyData; index as numeroInicio">
              <th scope="row">{{ numeroInicio + 1 }}</th>
              <td>{{ usuario.login }}</td>
              <td>{{ usuario.modulo }}</td>
              <td>{{ usuario.description|titlecase}} <em *ngIf="usuario.idModulo==1">{{usuario.ip}}</em></td>
              <td>{{ usuario.createdDate}}</td>
              <td>
                  <button class="btn btn-primary"
                  *ngIf="usuario.idModulo == 8 || usuario.idModulo == 9 || usuario.idModulo == 10 || usuario.idModulo == 11"
                  (click)="openModal(report91011,numeroInicio)"><em class="pi pi-eye"></em></button>

                  <button class="btn btn-primary"
                  *ngIf="usuario.idModulo == 5"
                  (click)="openModal(guarda,numeroInicio)"><em class="pi pi-eye"></em></button>

                  <button class="btn btn-primary"
                  *ngIf="usuario.idModulo == 6"
                  (click)="openModal(cambios,numeroInicio)"><em class="pi pi-eye"></em></button>

                  <button class="btn btn-primary"
                  *ngIf="usuario.idModulo == 13"
                  (click)="openModal(descarga,numeroInicio)"><em class="pi pi-eye"></em></button>
              </td>
            </tr>
          </tbody>

        </table>
      </div>
    </div>
  </div>
</div>

<ng-template #cambios let-modal>
  <div class="modal-header">
    <h4 *ngIf="!editState" class="modal-title">{{bodyData[numeroFilaActualizar].description}}</h4>
    <button class="close" aria-label="close" type="button" (click)="modal.dismiss()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <table class="table table-striped" aria-label="">
      <thead>
        <tr>
          <th scope="col">Campo</th>
          <th scope="col">Antiguo</th>
          <th scope="col">Nuevo</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Rut</td>
          <td>{{jsonOld.rut}}</td>
          <td>{{jsonNew.rut}}</td>
        </tr>
        <tr>
          <td>Nombres</td>
          <td>{{jsonOld.name}}</td>
          <td>{{jsonNew.name}}</td>
        </tr>
        <tr>
          <td>Apellidos</td>
          <td>{{jsonOld.lastName}}</td>
          <td>{{jsonNew.lastName}}</td>
        </tr>
        <tr>
          <td>Correo Electronico</td>
          <td>{{jsonOld.mail}}</td>
          <td>{{jsonNew.mail}}</td>
        </tr>
        <tr>
          <td>Teléfono</td>
          <td>{{jsonOld.phoneNumber}}</td>
          <td>{{jsonNew.phoneNumber}}</td>
        </tr>
        <tr>
          <td>Unidad</td>
          <td>{{jsonOld.unit}}</td>
          <td>{{jsonNew.unit}}</td>
        </tr>
        <tr>
          <td>Perfil</td>
          <td>{{jsonOld.profiles}}</td>
          <td>{{jsonNew.profile.description}}</td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="modal-footer">

  </div>
</ng-template>

<ng-template #guarda let-modal>
  <div class="modal-header">
    <h4 *ngIf="!editState" class="modal-title">{{bodyData[numeroFilaActualizar].description}}</h4>
    <button class="close" aria-label="close" type="button" (click)="modal.dismiss()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <table class="table table-striped" aria-label="">
      <thead>
        <tr>
          <th scope="col">Campo</th>
          <th scope="col">Valor</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Rut</td>
          <td>{{jsonNew.rut}}</td>
        </tr>
        <tr>
          <td>Nombres</td>
          <td>{{jsonNew.name}}</td>
        </tr>
        <tr>
          <td>Apellidos</td>
          <td>{{jsonNew.lastName}}</td>
        </tr>
        <tr>
          <td>Correo Electronico</td>
          <td>{{jsonNew.mail}}</td>
        </tr>
        <tr>
          <td>Teléfono</td>
          <td>{{jsonNew.phoneNumber}}</td>
        </tr>
        <tr>
          <td>Unidad</td>
          <td>{{jsonNew.unit}}</td>
        </tr>
        <tr>
          <td>Perfil</td>
          <td>{{jsonNew.profile.description}}</td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="modal-footer">

  </div>
</ng-template>

<ng-template #report91011 let-modal>
  <div class="modal-header">
    <h4 *ngIf="!editState" class="modal-title">{{bodyData[numeroFilaActualizar].description}}</h4>
    <button class="close" aria-label="close" type="button" (click)="modal.dismiss()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <table class="table table-striped" aria-label="">
      <thead>
        <tr>
          <th scope="col">Campo</th>
          <th scope="col">Dato</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Usuario</td>
          <td>{{datosVisualizar.login}}</td>
        </tr>
        <tr>
          <td>Descripción</td>
          <td>{{datosVisualizar.description}}</td>
        </tr>
        <tr>
          <td>Fecha de Consulta</td>
          <td>{{datosVisualizar.createdDate}}</td>
        </tr>
        <tr>
          <td>Ip</td>
          <td>{{datosVisualizar.ip}}</td>
        </tr>
        <tr *ngIf="jsonNew.filters.numberMobil!=''">
          <td>Teléfono</td>
          <td>{{jsonNew.filters.numberMobil}}</td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="modal-footer">
  </div>
</ng-template>

<ng-template #descarga let-modal>
  <div class="modal-header">
    <h4 *ngIf="!editState" class="modal-title">{{bodyData[numeroFilaActualizar].description}}</h4>
    <button class="close" aria-label="close" type="button" (click)="modal.dismiss()">
      <span aria-hidden="true">&times;</span>
    </button>
  </div>
  <div class="modal-body">
    <table class="table table-striped" aria-label="">
      <thead>
        <tr>
          <th scope="col">Campo</th>
          <th scope="col">Dato</th>
        </tr>
      </thead>
      <tbody>
        <tr>
          <td>Usuario</td>
          <td>{{datosVisualizar.login}}</td>
        </tr>
        <tr>
          <td>Descripcion</td>
          <td>{{datosVisualizar.description}}</td>
        </tr>
        <tr>
          <td>Fecha Descarga</td>
          <td>{{datosVisualizar.createdDate}}</td>
        </tr>
        <tr>
          <td>Ip Usuario</td>
          <td>{{datosVisualizar.ip}}</td>
        </tr>
      </tbody>
    </table>
  </div>
  <div class="modal-footer">
  </div>
</ng-template>

