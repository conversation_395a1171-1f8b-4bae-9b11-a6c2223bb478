import { AuthService } from '@modules/auth/services/auth.service';
import { Component, OnInit } from '@angular/core';
import { ReportService } from '@modules/report/services/report.service';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';
import { HeaderReportService } from '@shared/services/header-report.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { ResponseIntDto } from '@core/models/report/responseIntDto.model';
import { ReportDto } from '@core/models/report/reportDto.model';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-audit-pages',
  templateUrl: './audit-pages.component.html',
  styleUrls: ['./audit-pages.component.scss']
})
export class AuditPagesComponent implements OnInit {

  editState: boolean = false;
  bodyData: any = [];
  txtMessage: string = "";
  numeroFilaActualizar:any;

   datosReporte: any = {
      "page": 0,
      "size": 10,
      "filters": {
          "numberMobil": "",
          "idAccount": 0,
          "idCompany": 0,
          "idStatus": 0,
          "idInputMode": 0,
          "dateFrom": "",
          "dateTo": ""
      },
      "ip": ""
 }

  totalRecords: any;
  rowsPage: number=10;
  page: number=0;
  txtMessageDataView: string="";
  defaultDate: any;
  defaultDate2: any;
  numeroInicio: number=0;
  datosAuditoria:any;
  datosVisualizar:any;
  jsonOld: any;
  jsonNew: any;
  subcriptionAudit: any;


  constructor(
    public _modal: NgbModal,
    private _spinner: NgxSpinnerService,
    private _headerService: HeaderReportService,
    private _reportService: ReportService,
    private _authService: AuthService
  ) { }


  ngOnInit(): void {

    this._headerService.reporteSeleccionado$.emit('audit');
      this.subcriptionAudit = this._headerService.datosReporte$.subscribe(
       (data: any) => {
         this.datosReporte.filters.dateFrom = data.filters.dateFrom;
         this.datosReporte.filters.dateTo = data.filters.dateTo;
         this.datosReporte.filters.idAccount = data.filters.idAccount;
         this.datosReporte.filters.idModule = data.filters.idModule;
         this.datosReporte.page = data.page;
         this.datosReporte.size = data.size;
         this.obtenerDataAudit(this.datosReporte);
       }
     );
   }

   ngAfterViewInit(): void {
     this.numeroInicio;

   }

   ngOnDestroy(): void {
    console.log('audit');
    this.subcriptionAudit.unsubscribe();
   }


   obtenerDataAudit(filter:any) {
    this._spinner.show();
     this._reportService.getDataAudit(this.datosReporte)
       .subscribe(
         (obj: any) => {
           this.bodyData = obj.data;
           this.totalRecords= obj.countRows;
           this.printMessagePages();
           this._spinner.hide();
           if(this.totalRecords==0){
             Swal.fire("Lo sentimos","No se encontraron datos ","warning");
                                   }
         },
         (errorData: any) => {
           this.txtMessage = errorData.error;
           console.log(errorData);
           this._spinner.hide();
         }
       );
   }

   onPageChange(event: any) {
     this.datosReporte.size=event.rows
     this.datosReporte.page = event.page;
     this.obtenerDataAudit(this.datosReporte);
   }

   private printMessagePages() {
     let init = this.datosReporte.page * this.datosReporte.size;
     let end = init + this.datosReporte.size;
     this.numeroInicio=init;
     this.txtMessageDataView = "mostrando los registros del " + (init + 1) + " al " + end + " de un total de " + this.totalRecords;
   }

   openModal(contenido :any,numeroFilaActualizar:any){
     this.numeroFilaActualizar=numeroFilaActualizar;
     this.datosVisualizar=this.bodyData[numeroFilaActualizar];

     this.datosAuditoria= this.bodyData[numeroFilaActualizar].newData;
    if(this.datosAuditoria != ""){
     this.jsonNew = JSON.parse(this.datosAuditoria.replace('/', ''));
    }


     this.datosAuditoria= this.bodyData[numeroFilaActualizar].oldData;
     if(this.datosAuditoria != ""){
       this.jsonOld = JSON.parse(this.datosAuditoria.replace('/', ''));
     }

     this._modal.open(contenido, { size: 'lg' });
   }

   generateReportExcelPage(){
     Swal.fire("Estamos trabajando para su comodida","La accion que quiere realizar, estara habilitada proximamente","warning");
   }
   generateReportExcelFull(){
     let reportDto: ReportDto = this.datosReporte;
     this._reportService.generateCvsReportLoadDetailHistoricalFullAudit(reportDto)
       .subscribe(
         (obj: ResponseIntDto) => {
           let idReport = obj.data;
           Swal.fire("Exito","Se ha agregado el la solicitud de reporte con el id("+idReport+") a la cola de generación de reportes","success");
         },
         (errorData) => {
           this.txtMessage = errorData.error;
           console.log(errorData);
         }
       );


   }
}
