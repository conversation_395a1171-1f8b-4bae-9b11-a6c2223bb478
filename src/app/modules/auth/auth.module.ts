import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { AuthRoutingModule } from './auth-routing.module';
import { AuthPagesComponent } from './pages/auth-pages/auth-pages.component';
import { LoginPageComponent } from './pages/login-page/login-page.component';
import { ForgotPageComponent } from './pages/forgot-page/forgot-page.component';
import { ReactiveFormsModule } from '@angular/forms';
import {ButtonModule} from 'primeng/button';


@NgModule({
  declarations: [
    AuthPagesComponent,
    LoginPageComponent,
    ForgotPageComponent
  ],
  imports: [
    CommonModule,
    AuthRoutingModule,
    ReactiveFormsModule,
    ButtonModule
  ]
})
export class AuthModule { }
