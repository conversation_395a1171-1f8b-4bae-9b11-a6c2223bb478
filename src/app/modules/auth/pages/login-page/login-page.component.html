<div class=" col-12  col-sm-12 offset-md-8 col-md-3 mt-5">
  <form [formGroup]="formLogin" (ngSubmit)="sendLogin()">
      <div class="card">
          <div class="card-body">
              <h5 class="card-title text-center">Inicio de Sesi&oacute;n</h5>
              <div class="col-sm-12">
                  <div class="p-fluid">
                      <div class="p-field p-3">
                          <label for="txtusername" class="my-2">Usuario</label>
                          <div class="input-group">
                              <input id="txtusername" type="text" class="form-control" formControlName="user" />
                              <div class="input-group-append">
                                  <span class="input-group-text">
                                      <em class="pi pi-user my-1"></em>
                                  </span>
                              </div>
                          </div>
                          <div *ngIf="formLogin.controls['user'].invalid && (formLogin.controls['user'].dirty || formLogin.controls['user'].touched)"
                              class="error mt-1">
                              <div *ngFor="let validation of smsValidaciones.user">
                                  <span
                                      *ngIf="formLogin.controls['user'].hasError(validation.type) && (formLogin.controls['user'].dirty || formLogin.controls['user'].touched)">{{validation.message}}</span>
                              </div>
                          </div>
                      </div>

                      <div class="p-field p-3">
                          <label for="txtpassword" class="my-2">Contrase&ntilde;a</label>
                          <div class="input-group">
                              <input id="txtpassword" [type]="fieldTextType ? 'text' : 'password'"
                                  class="form-control" formControlName="password" />
                              <span class="input-group-text">
                                  <em class="py-1 pi"
                                      [ngClass]="{'pi-eye': !fieldTextType,'pi-eye-slash ': fieldTextType}"
                                      (click)="toggleFieldTextType()"></em>
                              </span>
                          </div>
                          <div *ngIf="formLogin.controls['password'].invalid && (formLogin.controls['password'].dirty || formLogin.controls['password'].touched)"
                              class="error mt-1">
                              <div *ngFor="let validation of smsValidaciones.password">
                                  <span
                                      *ngIf="formLogin.controls['password'].hasError(validation.type) && (formLogin.controls['password'].dirty || formLogin.controls['password'].touched)">{{validation.message}}</span>
                              </div>
                          </div>
                      </div>

                      <div class="p-field p-3">
                          <p-button label="Login" class="col-md-12" type="submit" [disabled]="!formLogin.valid"
                              styleClass="p-button-secondary" icon="pi pi-sign-in" iconPos="right"
                              [loading]="loading[0]"></p-button>
                      </div>
                      <div class="p-field text-center p-3">
                        <p><a id="link" [routerLink]="['/auth/forgot']">Olvide mi contraseña</a></p>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </form>
</div>
