import { AuthService } from './../../services/auth.service';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { TokenService } from '@modules/auth/services/token.service';
import { Router } from '@angular/router';
import { SharedService } from '@shared/services/shared.service';

import Swal from 'sweetalert2'
import { UserService } from '@modules/maintenance/services/user.service';
import { NgxSpinnerService } from 'ngx-spinner';


@Component({
  selector: 'app-login-page',
  templateUrl: './login-page.component.html',
  styleUrls: ['./login-page.component.scss']
})
export class LoginPageComponent implements OnInit {
  formLogin:FormGroup= new FormGroup({});

  fieldTextType: boolean=false;
  txtpassword: string = "";
  txtusername: string = "";
  txtMessage: string = "";
  loading = [false];
  passwordPattern:any=/^(?!.*?[0-9a-zA-Z]+$)/;
  smsValidaciones: any;
  nombreUsuario:string="";

  constructor(
              private _authService:AuthService,
              private _tokenService:TokenService,
              private _sharedService:SharedService,
              private _userService:UserService,
              private router:Router,
              private _spinner: NgxSpinnerService
              ) {
                this.smsValidaciones = {
                  user: [
                      { type: 'minlength', message: 'Usuario debe tener al menos 5 caracteres' },
                      { type: 'required', message: 'Se te ha olvidado ingresar el Usuario' },
                      { type: 'pattern',   message: 'El nombre de usuario no debe contener espacios ni caracteres especiales' }

                  ],
                  password: [
                    { type: 'required',  message: 'Se te ha olvidado ingresar la Password' },
                    { type: 'minlength', message: 'Contraseña debe tener minimo 8 caracteres'},
                    { type: 'maxlength', message: 'Contraseña no puede tener más de 36 caracteres' },
                    { type: 'pattern',   message: 'Contraseña debe tener mínimo un número, una minúscula y una mayúscula, sin caracteres especiales o espacios' }
                  ]
                };
              }

  ngOnInit(): void {
    this.formLogin = new FormGroup(
      {
        password:new FormControl('',
          [Validators.required,
           Validators.minLength(8),
           Validators.maxLength(36),
           Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*?[0-9a-zA-Z]+$)/)
          ]),
        user:new FormControl('',
          [Validators.required,
           Validators.minLength(5),
           Validators.pattern(/^(?=.*?[0-9a-zA-Z]+$)/)
          ])
      }
    );

    this._sharedService.getIPLocal()
    .subscribe((obj: any) => {
      sessionStorage.setItem("ip",obj.ip);
    });
  }
//corregir callback hell
//corregir obtener datos de usuario y guardarlo en servicio
//corregir el acceso a la ip
  sendLogin():void{
    this._spinner.show();
    let ip =sessionStorage.getItem("ip")+"";
    const{user, password } = this.formLogin.value;
    this._authService.getTokenApi(user,password,ip)
    .subscribe(responseOk=>{
      this._userService.getOneUser(user).subscribe(
        userData=>{
          sessionStorage.setItem('userData',JSON.stringify(userData.data));
          if(userData.data.changePassword==1){
            Swal.fire('Su contraseña fue reiniciada','Debes crear una nueva','warning');
            this.router.navigate(['/auth/changepassword']);
          }else if (userData.data.changePassword==2){
            Swal.fire('Su contraseña esta por caducar','Recuerda renovarla','warning');
            this.router.navigate(['/home']);
          }else if(userData.data.changePassword==3){
            Swal.fire('Su contraseña caduco','Debes crear una nueva','warning');
            this.router.navigate(['auth/changepassword']);
          }else{
            this.router.navigate(['/home']);
          }
              sessionStorage.setItem('limitSesion',userData.data.timeOutSession);
              this._spinner.hide();
        }
      );
    },
    err=>{
      console.log(err.error);
      Swal.fire('Error al Ingresar!!!', err.error, 'error');
      this._spinner.hide();
    });

  }

  toggleFieldTextType() {
    this.fieldTextType = !this.fieldTextType;
    }
}
