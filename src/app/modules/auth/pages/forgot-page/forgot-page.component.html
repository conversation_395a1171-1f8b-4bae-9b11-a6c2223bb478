<div class=" col-12  col-sm-12 offset-md-8 col-md-3 mt-5">
  <form [formGroup]="forgotPasswordForm" (ngSubmit)="sendData()">
      <div class="card">
          <div class="card-body">
              <h5 class="card-title text-center">Recuperar Contrase&ntilde;a</h5>
              <div class="col-12">
                  <div class="p-fluid">
                      <div class="p-field p-3">
                          <label for="txtusername" class="my-2">Usuario</label>
                          <div class="input-group">
                              <input id="txtusername" type="text" class="form-control" formControlName="user" />
                              <div class="input-group-append">
                                  <span class="input-group-text">
                                      <em class="pi pi-user my-1"></em>
                                  </span>
                              </div>
                          </div>
                          <div *ngIf="forgotPasswordForm.controls['user'].invalid && (forgotPasswordForm.controls['user'].dirty || forgotPasswordForm.controls['user'].touched)"
                              class="error mt-1">
                              <div *ngFor="let validation of smsValidaciones.user">
                                  <span
                                      *ngIf="forgotPasswordForm.controls['user'].hasError(validation.type) && (forgotPasswordForm.controls['user'].dirty || forgotPasswordForm.controls['user'].touched)">{{validation.message}}</span>
                              </div>
                          </div>
                      </div>
                      <div class="p-field p-3">
                          <label for="txtMail" class="my-2">Correo Electronico</label>
                          <div class="input-group">
                              <input id="txtMail" type="text" class="form-control" formControlName="email" />
                              <div class="input-group-append">
                                  <span class="input-group-text">
                                      <em>@</em>
                                  </span>
                              </div>
                          </div>
                          <div *ngIf="forgotPasswordForm.controls['email'].invalid && (forgotPasswordForm.controls['email'].dirty || forgotPasswordForm.controls['email'].touched)"
                              class="error mt-1">
                              <div *ngFor="let validation of smsValidaciones.email">
                                  <span
                                      *ngIf="forgotPasswordForm.controls['email'].hasError(validation.type) && (forgotPasswordForm.controls['email'].dirty || forgotPasswordForm.controls['email'].touched)">{{validation.message}}</span>
                              </div>
                          </div>
                      </div>
                  </div>
              </div>
              <div class="col-12">
                  <div class="row">
                      <div class="col-1"></div>
                      <div class="col-2 text-start">
                          <p-button label="Volver" styleClass="p-button-primary" [routerLink]="['/auth/login']" ></p-button>
                      </div>
                      <div class="col-8 text-end">
                          <p-button label="Solicitar" type="submit" [disabled]="!forgotPasswordForm.valid"
                              styleClass="p-button-secondary" icon="pi pi-sign-in" iconPos="right"
                              [loading]="loading[0]"></p-button>
                      </div>
                  </div>
              </div>
          </div>
      </div>
  </form>
</div>
