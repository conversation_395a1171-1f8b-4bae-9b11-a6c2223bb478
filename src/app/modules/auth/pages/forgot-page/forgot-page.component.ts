import { Router } from '@angular/router';
import { AuthService } from './../../services/auth.service';
import { Component, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import Swal from 'sweetalert2';
import { FieldsPassword } from '@core/models/user/fieldsPassword.model';
import { SharedService } from '@shared/services/shared.service';

@Component({
  selector: 'app-forgot-page',
  templateUrl: './forgot-page.component.html',
  styleUrls: ['./forgot-page.component.scss']
})
export class ForgotPageComponent implements OnInit {

  forgotPasswordForm:FormGroup= new FormGroup({});
  txtMail: string = "";
  txtMessage: string = "";
  loading = [false];
  emailPattern="^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z0-9.-]{2,4}$";
  smsValidaciones:any;

  constructor(
    private _authService:AuthService,
    private _sharedService:SharedService,
    private router:Router
  ) {
    this.smsValidaciones = {
      user: [
          { type: 'minlength', message: 'Usuario debe tener al menos 5 caracteres' },
          { type: 'required', message: 'Se te ha olvidado ingresar el Usuario' },
          { type: 'pattern',   message: 'El nombre de usuario no debe contener espacios ni caracteres especiales' }
      ],
      email: [
        { type: 'required',  message: 'Se te ha olvidado ingresar El correo' },
        { type: 'pattern',   message:  'Formato de Email incorrecto' }
      ]
    };

    this.forgotPasswordForm = new FormGroup({
      email:new FormControl('',[Validators.required,Validators.pattern(this.emailPattern)]),
      user:new FormControl('',[Validators.required,Validators.minLength(5),Validators.pattern(/^(?!.*?[#?!@$%^&*/_-\s])/)])
    });
  }

  ngOnInit(): void {
    // TODO document why this method 'ngOnInit' is empty

  }


   sendData():void{
    const{user, email } = this.forgotPasswordForm.value;
    if(this._sharedService.validarCaracterEspecial(user)){
      Swal.fire('Ocurrio Algo','El nombre de usuario no debe contener espacios ni caracteres especiales','error');
    }else{
      this.resetPassword(user,email);
    }

  }


  resetPassword(user:string,email:string){
    this._authService.forgotPassword(user,email)
    .subscribe(
      (data: FieldsPassword) => {
        if(data.code==0){
          Swal.fire('Peticion exitosa', data.message, 'success');
          this.router.navigate(['/auth/login']);
        }else{
          if (data.message == null){
            Swal.fire('Ha ocurrido algo',"houston tenemos un problema", 'error');
          }else{
            Swal.fire('Ha ocurrido algo',data.message, 'error');
          }
        }
        return 0;
      },
      err=>{
        Swal.fire('Error al enviar', err.message, 'error');
      }
    );
  }

}
