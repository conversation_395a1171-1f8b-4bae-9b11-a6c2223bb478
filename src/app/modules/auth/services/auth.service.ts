import { Router } from '@angular/router';
import { TokenService } from './token.service';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable ,tap} from 'rxjs';
import { environment } from 'src/environments/environment';
import { sha256 } from 'js-sha256';
import { FieldsPassword } from '@core/models/user/fieldsPassword.model';
import { ChangePassword } from '@core/models/auth/changePassword.model';
import { NgbModal } from '@ng-bootstrap/ng-bootstrap';



@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private readonly URL =environment.urlAccountService;

  CHECK_INTERVAL = 15000 // in ms

  constructor(
    private http:HttpClient,
    private _tokenService:TokenService,
    private _modalService:NgbModal,
    private router:Router) {

    this.check();
    this.initListener();
    this.initInterval();
    sessionStorage.setItem('lastAction',Date.now().toString());


  }

  public getLastAction() {
    let value = sessionStorage.getItem('lastAction');
    let result = 0;
    if(value != null){
      result = parseInt(value);
    }
    return result;
  }
  public setLastAction(lastAction: number) {
    sessionStorage.setItem('lastAction', lastAction.toString());
  }
  initListener(){
    document.body.addEventListener('click',()=> this.reset());
    document.body.addEventListener('mouseover',()=> this.reset());
    document.body.addEventListener('mouseout',()=> this.reset());
    document.body.addEventListener('keydown',()=> this.reset());
    document.body.addEventListener('keyup',()=> this.reset());
    document.body.addEventListener('keypress',()=> this.reset());
  }
  reset() {
    this.setLastAction(Date.now());
  }
  initInterval() {
    setInterval(() => {
      this.check();
    }, this.CHECK_INTERVAL);
  }
  check() {
    const now = Date.now();
    const timeleft = this.getLastAction() + parseInt(""+sessionStorage.getItem('limitSesion')) * 60 * 1000;
    const diff = timeleft - now;
    const isTimeout = diff < 0;

    if (isTimeout)  {
      this.logOut();
      this.router.navigate(['/']);
    }
  }

  getTokenApi(username:string, pwd:string,ip:string):Observable<any>{
    let password = sha256(pwd);
    const BODY = {
      username,
      password,
      ip
    };
    return this.http.post<any>(`${this.URL}/api/auth/login`,BODY)
    .pipe(
      tap(response => this._tokenService.saveToken(response))
    )
  }

  forgotPassword(usuario:any,email:any):Observable<any>{
    const TOKEN = this._tokenService.getToken()+"";
    let ip = sessionStorage.getItem('ip');
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
     return this.http.get<any>(`${this.URL}/api/user/forgotPassword/`+usuario+"/"+email+"/"+ip,options);
   }

   //corregir encriptado de password
   changePassword(body:ChangePassword):Observable<any>{
    const TOKEN = this._tokenService.getToken()+"";
    const BODY = body;
    BODY.password= sha256( body.password);
    BODY.oldPassword= sha256( body.oldPassword)

    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
     return this.http.post<FieldsPassword>(`${this.URL}/api/user/changePass`,BODY,options);
   }

   logOut(){
    this._tokenService.burnToken().subscribe(response=>{
      sessionStorage.clear();
      this._modalService.dismissAll();
    });
    this.router.navigate(['/auth/login']);
   }
}
