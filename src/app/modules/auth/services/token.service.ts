import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class TokenService {
  private readonly URL =environment.urlAccountService;
token:string | null = null;

constructor(
  private http:HttpClient,
){}

  saveToken(token:any):void{
    this.token = token.token
  }
  getToken(){
    return this.token;
  }
  clearToken():void{
    this.token =null;
  }

  burnToken(){
    let ip=sessionStorage.getItem("ip");
     const TOKEN = this.getToken()+"";
     let headers = new HttpHeaders({
       'Content-Type': 'application/json',
       'Authorization':TOKEN});
     let options = { headers: headers };
      return this.http.get<any>(`${this.URL}/api/auth/burn/token/`+ip,options);
  }
}
