import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ReportRoutingModule } from './report-routing.module';
import { QueueReportPageComponent } from './pages/queue-report-page/queue-report-page.component';
import { ConsolidatedReportPageComponent } from './pages/consolidated-report-page/consolidated-report-page.component';
import { ReportPageComponent } from './pages/report-page/report-page.component';
import { SharedModule } from '@shared/shared.module';
import { DetailReportPageComponent } from './pages/detail-report-page/detail-report-page.component';


@NgModule({
  declarations: [
    QueueReportPageComponent,
    ConsolidatedReportPageComponent,
    ReportPageComponent,
    DetailReportPageComponent
  ],
  imports: [
    CommonModule,
    ReportRoutingModule,
    SharedModule,
    NgbModule
  ]
})
export class ReportModule { }
