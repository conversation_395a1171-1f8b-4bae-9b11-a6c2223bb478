import { Component, OnInit } from '@angular/core';
import { HeaderReportDto } from '@core/models/report/headerReportDto.model';
import { ReportDataDto } from '@core/models/report/reportDataDto.model';
import { ReportDto } from '@core/models/report/reportDto.model';
import { ResponseIntDto } from '@core/models/report/responseIntDto.model';
import { RowBodyReportDto } from '@core/models/report/rowBodyReportDto.model';
import { ReportService } from '@modules/report/services/report.service';
import { HeaderReportService } from '@shared/services/header-report.service';
import { SharedService } from '@shared/services/shared.service';
import { NgxSpinnerService } from 'ngx-spinner';
import { PrimeNGConfig } from 'primeng/api';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-detail-report-page',
  templateUrl: './detail-report-page.component.html',
  styleUrls: ['./detail-report-page.component.scss']
})
export class DetailReportPageComponent implements OnInit {

  txtMessageDataView: string = "";
  txtMessage: string = "";

  headerData: HeaderReportDto[] = [];
  bodyData: RowBodyReportDto[] = [];

  totalRecords: number = 0;
  rowsPage: number = 10;
  page: number = 0;
  datosReporte: any;
  subscriptionDetail: any;

  constructor(
    private _reportService: ReportService,
    private _headerReportService:HeaderReportService,
    private config: PrimeNGConfig,
    private spinner: NgxSpinnerService
  ) { }

  ngOnDestroy(): void {
    console.log('detail');
    this.subscriptionDetail.unsubscribe();
      }

  ngOnInit(): void {
    this._headerReportService.reporteSeleccionado$.emit('detalle');
    this._headerReportService.fechaSeleccionada$.emit();
    this.subscriptionDetail = this._headerReportService.datosReporte$.subscribe(
      (data: any) => {
        this.datosReporte = data;
        this.generateDataReport(data);
       }
      );
      }
  private printMessagePages() {
    let init = this.page * this.rowsPage;
    let end = init + this.rowsPage;
    this.txtMessageDataView = "mostrando los registros del " + (init + 1) + " al " + end + " de un total de " + this.totalRecords;
  }
  onPageChange(event: any) {
    this.rowsPage = event.rows;
    this.datosReporte.rows = this.rowsPage;
    this.datosReporte.page = event.page;
    this.datosReporte.size = this.rowsPage;
    this.page=event.page;
    this.generateDataReport(this.datosReporte);
  }
  generateReportExcelPage() {
    let reportDto: ReportDto = this.datosReporte;
    this._reportService.generateCvsReportLoadDetailHistoricalPage(reportDto)
      .subscribe(
        (obj: ResponseIntDto) => {
          let idReport = obj.data;
          Swal.fire("Exito","Se ha agregado el la solicitud de reporte con el id("+idReport+") a la cola de generación de reportes","success");
        },
        (errorData:any) => {
          this.txtMessage = errorData.error;
          console.log(errorData);
        }
      );
  }

  generateReportExcelFull() {
    let reportDto: ReportDto = this.datosReporte;
    this._reportService.generateCvsReportLoadDetailHistoricalFull(reportDto)
      .subscribe(
        (obj: ResponseIntDto) => {
          let idReport = obj.data;
          Swal.fire("Exito","Se ha agregado el la solicitud de reporte con el id("+idReport+") a la cola de generación de reportes","success");
        },
        (errorData:any) => {
          this.txtMessage = errorData.error;
          console.log(errorData);
        }
      );
  }

  generateDataReport(data: any) {
    this.spinner.show();
    this.txtMessage = "";
    let reportDto: ReportDto = data;
    this._reportService.getDataReportDetailHistorical(reportDto)
      .subscribe(
        (obj: ReportDataDto) => {
          obj.data.headers[0].name =obj.data.headers[0].name.replace("Movil","Móvil");
          obj.data.headers[4].name=obj.data.headers[4].name.replace("Fecha Envio","Fecha Envío");
          this.headerData = obj.data.headers;
          this.bodyData = obj.data.body;
          this.totalRecords = obj.countRows;
          this.printMessagePages();
          if(obj.data.body.length==0){
            Swal.fire("Lo sentimos","No se encontraron registros con los parámetros establecidos","success");
          }
          this.spinner.hide();
        },
        (err:any) => {
          this.txtMessage = err.error;
          console.log(err);
          this.spinner.hide();
        }
      );
  }

}
