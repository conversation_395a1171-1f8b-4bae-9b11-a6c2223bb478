<div class="bg-white mb-3 h100">
  <div *ngIf="totalRecords" class="card mt-3">
    <div class="row">
        <div class="offset-md-6 col-6 text-end">
            <button *ngIf="totalRecords!=0" (click)="generateReportExcelPage()" class="btn btn-primary m-1">Generar
                Pagina Actual Reporte</button>
            <button *ngIf="totalRecords!=0" (click)="generateReportExcelFull()" class="btn btn-primary m-1">Generar
                Reporte Completo</button>
        </div>
    </div>
    <div class="row">
        <div class="col-12  " [ngClass]="{ 'invisible': totalRecords == 0, 'visible': totalRecords != 0}">
            <div class="col-12">
                <p-paginator [rows]="rowsPage" [totalRecords]="totalRecords" [rowsPerPageOptions]="[10,20,30]"
                    (onPageChange)="onPageChange($event)"></p-paginator>
            </div>
            <div class="col-12 my-2 text-center">{{txtMessageDataView}}</div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 my-2">
            <div class="col-12 text-center">{{txtMessage}}</div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 table-responsive">
            <table class="table table-striped">
                <thead id="headReport">
                    <th *ngFor="let header of headerData">{{header.name}}</th>
                </thead>
                <tbody id="bodyReport">
                    <tr *ngFor="let RowBodyReportDto of bodyData">
                        <td *ngFor="let value of RowBodyReportDto.cells">{{value}}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
  </div>
</div>
