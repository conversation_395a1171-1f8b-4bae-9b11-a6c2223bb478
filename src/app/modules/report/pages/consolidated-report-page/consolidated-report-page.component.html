<div class="bg-white h100">
  <div *ngIf="totalRecords>1" class="card mt-3">
    <div class="row">
    <div class="col-12 my-2">
        <div class="col-12 text-center">{{txtMessage}}</div>
    </div>
  </div>
  <div class="row">
    <div class="col-12 table-responsive">
        <table class="table table-striped" aria-label="">
            <thead id="headReport">
                <th *ngFor="let header of headerData">{{header.name}}</th>
            </thead>
            <tbody id="bodyReport">
                <tr *ngFor="let RowBodyReportDto of bodyData">
                    <td *ngFor="let value of RowBodyReportDto.cells">{{value}}</td>
                </tr>
            </tbody>
        </table>
    </div>
  </div>
  </div>
</div>
