import { ReportService } from '@modules/report/services/report.service';
import { Component, OnInit } from '@angular/core';
import { ReportDataDto } from '@core/models/report/reportDataDto.model';
import { ReportDto } from '@core/models/report/reportDto.model';
import { ResponseIntDto } from '@core/models/report/responseIntDto.model';
import Swal from 'sweetalert2';
import { HeaderReportDto } from '@core/models/report/headerReportDto.model';
import { RowBodyReportDto } from '@core/models/report/rowBodyReportDto.model';
import { NgxSpinnerService } from 'ngx-spinner';
import { HeaderReportService } from '@shared/services/header-report.service';

@Component({
  selector: 'app-consolidated-report-page',
  templateUrl: './consolidated-report-page.component.html',
  styleUrls: ['./consolidated-report-page.component.scss']
})
export class ConsolidatedReportPageComponent implements OnInit {

  txtMessageDataView: string = "";
  txtMessage: string = "";
  headerData: HeaderReportDto[] = [];
  bodyData: RowBodyReportDto[] = [];
  totalRecords:number=0;
  dataReport:any;
  subcriptionConsolidated: any;

  constructor(
    private _reportService:ReportService,
    private _headerReportService:HeaderReportService,
    private _spinner: NgxSpinnerService
  ) { }

  ngOnInit(): void {
    this._headerReportService.reporteSeleccionado$.emit('consolidado');
    this._headerReportService.fechaSeleccionada$.emit();
    this._headerReportService.datosReporte$.subscribe(
      (data:any)=>{this.dataReport=data;}
    );
    this.subcriptionConsolidated = this._headerReportService.tipoReporteConsolidado$.subscribe(
      (datos:any)=>{
        if(datos==1){
          this.generateDataReport()
        }else if(datos==2){
          this.generateReportExcelConsolidated();
        }else{
          this.generateReportExcelDetailFull();
        }
      }
    );
  }

  ngOnDestroy(): void {
    console.log('consolidate');
    this.subcriptionConsolidated.unsubscribe();
  }

  generateReportExcelConsolidated(){
    let reportDto : ReportDto = this.dataReport;
    this._reportService.generateCvsReportLoadConsolidatedHistorical(reportDto)
    .subscribe(
      (obj:ResponseIntDto)=>{
        let idReport = obj.data;
        Swal.fire("Exito","Se ha agregado el la solicitud de reporte con el id("+idReport+") a la cola de generación de reportes","success");
      },
      (errorData)=>{
        this.txtMessage = errorData.error;
        console.log(errorData);
      }
    );
  }

  generateReportExcelDetailFull(){
    let reportDto : ReportDto = this.dataReport;
    this._reportService.generateCvsReportLoadDetailHistoricalFull(reportDto)
    .subscribe(
      (obj:ResponseIntDto)=>{
        let idReport = obj.data;
        Swal.fire("Exito","Se ha agregado el la solicitud de reporte con el id("+idReport+") a la cola de generación de reportes","success");
      },
      (errorData)=>{
        this.txtMessage = errorData.error;
        console.log(errorData);
      }
    );
  }

  generateDataReport(){
    this._spinner.show();
    this.txtMessage = "";
    let reportDto : ReportDto = this.dataReport;
    this._reportService.getDataReportConsolidatedHistorical(reportDto)
    .subscribe(
      (obj:ReportDataDto)=>{
        this.headerData = obj.data.headers;
        this.bodyData = obj.data.body;
        this.totalRecords = obj.data.body.length ;
        this._spinner.hide();
        if(this.totalRecords<=1){
          Swal.fire("Lo sentimos","No se encontraron registros con los parámetros establecidos","success");
        }
      },
      (errorData)=>{
        this.txtMessage = errorData.error;
        Swal.fire("Ha ocurrido algo","No se pudo generar el reporte","error")
        console.log(errorData);
        this._spinner.hide();
      }
    );
  }
}
