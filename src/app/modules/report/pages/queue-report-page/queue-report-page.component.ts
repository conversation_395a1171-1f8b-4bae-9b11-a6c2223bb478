import { SharedService } from '@shared/services/shared.service';
import { Component, OnInit } from '@angular/core';
import { QueueDto } from '@core/models/report/queueDto.model';
import { ReportDto } from '@core/models/report/reportDto.model';
import { ReportQueueDto } from '@core/models/report/reportQueueDto.model';
import { ReportService } from '@modules/report/services/report.service';
import * as FileSaver from 'file-saver';
import Swal from 'sweetalert2';
import { HeaderReportService } from '@shared/services/header-report.service';
import { NgxSpinnerService } from 'ngx-spinner';

@Component({
  selector: 'app-queue-report-page',
  templateUrl: './queue-report-page.component.html',
  styleUrls: ['./queue-report-page.component.scss']
})
export class QueueReportPageComponent implements OnInit {

  txtMessageDataView: string = "";
  txtMessage: string = "";

  headerData: string[] = [];
  bodyData: QueueDto[] = [];

  totalRecords: number = 0;
  rowsPage: number = 10;
  page: number = 0;
  osdo!:ReportDto;

  datosReporte: any;
  subcriptionQueue: any;
  constructor(
    private _reportService:ReportService,
    private _headerReportService:HeaderReportService,
    private _spinner:NgxSpinnerService

  ) { }

  ngOnInit(): void {
    this._headerReportService.reporteSeleccionado$.emit('cola');
    this._headerReportService.fechaSeleccionada$.emit();
    this.subcriptionQueue = this._headerReportService.datosReporte$.subscribe(
      (data: any) => {
        this.datosReporte = data;
        this.generateDataReport(data);
      }
    );
  }
  ngOnDestroy(): void {
    console.log('queeue');
    this.subcriptionQueue.unsubscribe();
  }

  private printMessagePages() {
    let init = this.page * this.rowsPage;
    let end = init + this.rowsPage;
    this.txtMessageDataView = "mostrando los registros del " + (init + 1) + " al " + end + " de un total de " + this.totalRecords;
  }

  onPageChange(event: any) {
    this.rowsPage = event.rows;
    this.datosReporte.rows = this.rowsPage;
    this.datosReporte.page = event.page;
    this.datosReporte.size = this.rowsPage;
    this.page=event.page;
    this.generateDataReport(this.datosReporte);
  }

  generateDataReport(datos: any) {
    this._spinner.show();
    this.txtMessage = "";
    let reportDto: ReportDto = datos;
    this._reportService.getDataReportQueueReport(reportDto)
      .subscribe(
        (obj: ReportQueueDto) => {
          this.headerData = ["id", "Usuario", "estado", "archivo", "fecha", "tipo Reporte", "tipo Pagina"];
          this.bodyData = obj.data;
          this.totalRecords = obj.countRows;
          if(this.totalRecords<=0){
            Swal.fire("Lo sentimos","No se encontraron registros con los parámetros establecidos","success");
          }
          this.printMessagePages();
          this._spinner.hide();
        },
        (errorData) => {
          this.txtMessage = errorData.error;
          console.log(errorData);
          this._spinner.hide();
        }
      );
  }

  dowloadFileReport(id: number) {
    this._reportService.downloadReport(id,this.datosReporte.ip).subscribe(
      (data) => {
        const blob = new Blob([data], { type: data.type });
        const csv = window.URL.createObjectURL(blob);

        if (data.type == 'application/zip') {
          FileSaver.saveAs(csv, 'Reporte_' + id + '.zip');
          console.log(data);
        } else {
          FileSaver.saveAs(csv, 'Reporte_' + id + '.csv');
          console.log(data);
        }
      }
    );
    console.log(id);
  }

}
