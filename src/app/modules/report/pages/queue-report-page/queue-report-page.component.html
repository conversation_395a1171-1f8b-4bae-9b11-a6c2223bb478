


<div class="bg-white h100">
  <div *ngIf="totalRecords" class="card mt-3 ">
    <div class="row">
        <div class="col-12  " [ngClass]="{ 'invisible': totalRecords == 0, 'visible': totalRecords != 0}">
            <div class="col-12">
                <p-paginator [rows]="rowsPage" [totalRecords]="totalRecords" [rowsPerPageOptions]="[10,20,30]"
                    (onPageChange)="onPageChange($event)"></p-paginator>
            </div>
            <div class="col-12 my-2 text-center">{{txtMessageDataView}}</div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 my-2">
            <div class="col-12 text-center">{{txtMessage}}</div>
        </div>
    </div>
    <div class="row">
        <div class="col-12 table-responsive">
            <table class="table table-striped" aria-label="">
                <thead id="headReport">
                    <th *ngFor="let text of headerData">{{text|titlecase}}</th>
                </thead>
                <tbody id="bodyReport">
                    <tr *ngFor="let queueDto of bodyData">
                        <td>{{queueDto.id}}</td>
                        <td>{{queueDto.nameAccount}}</td>
                        <td>{{queueDto.status}}</td>
                        <td>{{queueDto.filename}}</td>
                        <td>{{queueDto.createdDate}}</td>
                        <td>{{queueDto.typeReport}}</td>
                        <td>{{queueDto.typePage}}</td>
                        <td>
                            <button class="btn btn-primary"
                                *ngIf="queueDto.status=='PROCESADO' || queueDto.status=='DESCARGADO'"
                                (click)="dowloadFileReport(queueDto.id)">Descargar</button>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
  </div>
</div>

