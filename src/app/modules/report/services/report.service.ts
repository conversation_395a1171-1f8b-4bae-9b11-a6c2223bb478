import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { UsuarioDto } from '@core/models/audit/audit.model';
import { ReportDataDto } from '@core/models/report/reportDataDto.model';
import { ReportDto } from '@core/models/report/reportDto.model';
import { ReportQueueDto } from '@core/models/report/reportQueueDto.model';
import { ResponseIntDto } from '@core/models/report/responseIntDto.model';
import { TokenService } from '@modules/auth/services/token.service';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class ReportService {
  private readonly URL =environment.urlReportService;

  constructor(
    private http:HttpClient,
    private _tokenService:TokenService
  ) { }

  public getDataReportDetailDaily(report:ReportDto):Observable<ReportDataDto>{
    const TOKEN = this._tokenService.getToken()+"";
    const BODY = report;
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.post<ReportDataDto>(`${this.URL}/api/mt/load/detail/data/daily`,BODY,options);
  }

  public generateCvsReportLoadDetailDailyPage(report:ReportDto):Observable<ResponseIntDto>{
    const TOKEN = this._tokenService.getToken()+"";
    const BODY = report;
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.post<ResponseIntDto>(`${this.URL}/api/mt/load/detail/data/daily`,BODY,options);
  }

  public generateCvsReportLoadDetailDailyFull(report:ReportDto):Observable<ResponseIntDto>{
    const TOKEN = this._tokenService.getToken()+"";
    const BODY = report;
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.post<ResponseIntDto>(`${this.URL}/api/mt/load/detail/queue/csv/daily/full`,BODY,options);
  }

  public getDataReportDetailHistorical(report:ReportDto):Observable<ReportDataDto>{
    const TOKEN = this._tokenService.getToken()+"";
    const BODY = report;
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.post<ReportDataDto>(`${this.URL}/api/mt/load/detail/data/historical`,BODY,options);
  }

  public generateCvsReportLoadDetailHistoricalFull(report:ReportDto):Observable<ResponseIntDto>{
    const TOKEN = this._tokenService.getToken()+"";
    const BODY = report;
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.post<ResponseIntDto>(`${this.URL}/api/mt/load/detail/queue/csv/historical/full`,BODY,options);
  }

  public generateCvsReportLoadDetailHistoricalFullAudit(report:ReportDto):Observable<ResponseIntDto>{
    const TOKEN = this._tokenService.getToken()+"";
    const BODY = report;
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.post<ResponseIntDto>(`${this.URL}/api/mt/load/detail/queue/csv/audit/full`,BODY,options);
  }

  public generateCvsReportLoadDetailHistoricalPage(report:ReportDto):Observable<ResponseIntDto>{
    const TOKEN = this._tokenService.getToken()+"";
    const BODY = report;
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.post<ResponseIntDto>(`${this.URL}/api/mt/load/detail/queue/csv/historical/page`,BODY,options);
  }

  public getDataReportConsolidatedDaily(report:ReportDto):Observable<ReportDataDto>{
    const TOKEN = this._tokenService.getToken()+"";
    const BODY = report;
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.post<ReportDataDto>(`${this.URL}/api/mt/load/consolidated/data/historical`,BODY,options);
  }

  public generateCvsReportLoadConsolidatedDaily(report:ReportDto):Observable<ResponseIntDto>{
    const TOKEN = this._tokenService.getToken()+"";
    const BODY = report;
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.post<ResponseIntDto>(`${this.URL}/api/mt/load/consolidated/queue/csv/daily`,BODY,options);
  }

  public getDataReportConsolidatedHistorical(report:ReportDto):Observable<ReportDataDto>{
    const TOKEN = this._tokenService.getToken()+"";
    const BODY = report;
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.post<ReportDataDto>(`${this.URL}/api/mt/load/consolidated/data/historical`,BODY,options);
  }

  public generateCvsReportLoadConsolidatedHistorical(report:ReportDto):Observable<ResponseIntDto>{
    const TOKEN = this._tokenService.getToken()+"";
    const BODY = report;
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.post<ResponseIntDto>(`${this.URL}/api/mt/load/consolidated/queue/csv/historical`,BODY,options);
  }

  public getDataReportQueueReport(report:ReportDto){
    const TOKEN = this._tokenService.getToken()+"";
    const BODY = report;
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.post<ReportQueueDto>(`${this.URL}/api/queueReport/new/list`,BODY,options);
  }

  downloadReport(id:any, ipLocal:any){
    const TOKEN = this._tokenService.getToken()+"";
    let ip = sessionStorage.getItem('ip');
    let headers = new HttpHeaders({
      'Content-Type': 'aplication/json',
      'Authorization':TOKEN+"",
      'Data-Type':'application/octet-stream'});
    let options = { headers,responseType:'blob'as 'json' };
     return this.http.get<any>(`${this.URL}/api/download/file/`+id+"/"+ip,options);
  }

  getDataAudit(report: any):Observable<UsuarioDto>{
    const TOKEN = this._tokenService.getToken()+"";
    const BODY = report;
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.post<UsuarioDto>(`${this.URL}/api/queueReport/new/list/log`,BODY,options);
  }
}
