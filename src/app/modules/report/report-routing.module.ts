import { QueueReportPageComponent } from './pages/queue-report-page/queue-report-page.component';
import { ConsolidatedReportPageComponent } from './pages/consolidated-report-page/consolidated-report-page.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DetailReportPageComponent } from './pages/detail-report-page/detail-report-page.component';

const routes: Routes = [
  {
    path:'detail',
    component:DetailReportPageComponent
  },
  {
    path:'consolidate',
    component:ConsolidatedReportPageComponent
  },
  {
    path:'queue',
    component:QueueReportPageComponent
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class ReportRoutingModule { }
