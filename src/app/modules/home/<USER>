import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { HomeRoutingModule } from './home-routing.module';
import { HomePageComponent } from './pages/home-page/home-page.component';
import { SharedModule } from '@shared/shared.module';
import { HTTP_INTERCEPTORS } from '@angular/common/http';
import { InjetTokenInterceptor } from '@core/interceptors/injet-token.interceptor';



@NgModule({
  declarations: [
    HomePageComponent
  ],
  imports: [
    CommonModule,
    HomeRoutingModule,
    SharedModule,
  ],
  providers:[
    {
      provide:HTTP_INTERCEPTORS,
      useClass:InjetTokenInterceptor
    }
  ]
})
export class HomeModule { }
