import { ReportPageComponent } from './../report/pages/report-page/report-page.component';
import { MaintenancePageComponent } from './../maintenance/pages/maintenance-page/maintenance-page.component';
import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { AuditPagesComponent } from '@modules/audit/pages/audit-pages/audit-pages.component';
import { ChangePasswordComponent } from '@shared/components/change-password/change-password.component';

const routes: Routes = [
  {
    path:'audit',
    component:AuditPagesComponent,
    loadChildren:()=>import('@modules/audit/audit.module').then(m => m.AuditModule)
  },
  {
    path:'maintenance',
    component:MaintenancePageComponent,
    loadChildren:()=>import('@modules/maintenance/maintenance.module').then(m => m.MaintenanceModule)
  },
  {
    path:'report',
    component:ReportPageComponent,
    loadChildren:()=>import('@modules/report/report.module').then(m => m.ReportModule)
  },
  {
    path:'changepass',
    component:ChangePasswordComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class HomeRoutingModule { }
