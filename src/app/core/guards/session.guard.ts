import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivate, Router, RouterStateSnapshot, UrlTree } from '@angular/router';
import { TokenService } from '@modules/auth/services/token.service';
import { Observable } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class SessionGuard implements CanActivate {
  private router: Router;
  constructor(router: Router,private _tokenService:TokenService){
    this.router = router;
  }
  canActivate(
    _route: ActivatedRouteSnapshot,
    _state: RouterStateSnapshot): Observable<boolean | UrlTree> | Promise<boolean | UrlTree> | boolean | UrlTree {
      let token = this._tokenService.getToken();
      console.log('guarda de seguridad',token)
      if(token!=null){
        return true;
      }else{
        this.router.navigate(['/auth/login']);
        return false;
      }
  }
}
