export interface DataAudit {
         page : number,
         size : number,
         idAccountView : number,
         datefrom : any,
         dateTo :  any,
         logDto : {
             id : number,
             module : {
                 id : number,
                 description : any,
                 active : boolean
            },
             company : {
                 id : number,
                 nombreCorto : any
            },
             user : {
                 id : 0,
                 username :any
            },
             acction : any  ,
             createDate :any  ,
             description :any   ,
             data : {
                 oldData :any   ,
                 newData : any
            }
        }
    }

    export interface AuditDto{
             id : number,
             module : {
                 id : number,
                 description :string ,
                 active : any
            },
             company : {
                 id : number,
                 idTimeZone : number,
                 description : any,
                 maxTotalMsgs : number,
                 nombreCorto :  string
            },
             user : {
                 id : number,
                 idTimeZone : number,
                 username :  string ,
                 password : any,
                 oldPassword : any,
                 mail : string,
                 enabled : any,
                 changePassword : any,
                 lastChangePassword : any,
                 name : string,
                 lastName : string,
                 phoneNumber : any,
                 unit : any,
                 lastAccess : any,
                 rut : any,
                 profile : any,
                 company : any
            },
             acction : string ,
             createDate : any ,
             description :  any ,
             data : any
        }

  export interface UsuarioDto{
      id: number;
      idTimeZone: number;
      username: string;
      rut:number;
      password: any;
      oldPassword: any;
      mail: string;
      enabled: boolean;
      changePassword: boolean;
      lastChangePassword?: string;
      name: string;
      lastName: string;
      phoneNumber: any;
      unit: any;
      lastAccess: any;
      profile: {
          id: number;
          description:string;
          active: boolean
      },
      company: {
          id: number;
          idTimeZone: number;
          description: any;
          maxTotalMsgs: number;
          nombreCorto: string;
      }
  }
