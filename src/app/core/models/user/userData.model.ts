export interface UserData{

  code?:number
  data : {
      id : 1
      username :string
      password :string
      oldPassword : string
      mail :string
      enabled :boolean
      changePassword : number
      lastChangePassword : string
      name : string
      lastName : string
      phoneNumber : string
      unit : string
      timeOutSession:any
      profile : {
          id : number
          description :  string
          active : boolean
     }
 }
  countRows : 0
  message :  string
}
