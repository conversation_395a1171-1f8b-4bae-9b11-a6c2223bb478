import { ProfileDto } from "./profile.model";

export interface User {
    usuario: string;
    nombres: string;
    apellidos: string;
    email: string;
    telefono: number;
    unidad:any;
    estado:any;
    perfil:any;
  }

  export interface UserDto{
      id: number;
      idTimeZone: number;
      username: string;
      rut:number;
      createDate:string;
      password: any;
      oldPassword: any;
      mail: string;
      enabled: boolean;
      changePassword: number;
      lastChangePassword?: string;
      name: string;
      lastName: string;
      phoneNumber: any;
      unit: any;
      timeOutSession:any;
      lastAccess: any;
      profile: {
          id: number;
          description:string;
          active: boolean
      },
      company: {
          id: number;
          idTimeZone: number;
          description: any;
          maxTotalMsgs: number;
          nombreCorto: string;
      }
  }

  export class UserDto2{
    id: number = 0;
    username: string = "";
    password: string = "";
    oldPassword: string = "";
    mail: string = "";
    enabled: boolean = false;
    changePassword: number = 0;
    lastChangePassword: string = "";
    name: string = "";
    lastName: string = "";
    phoneNumber: string = "";
    unit: string = "";
    timeOutSession:number=0;
    ip?:number=0;
    profile: ProfileDto = new ProfileDto();
  }
