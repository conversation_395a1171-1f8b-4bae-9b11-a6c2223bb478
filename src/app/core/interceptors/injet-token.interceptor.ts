import { Injectable } from '@angular/core';
import {
  HttpRequest,
  HttpHandler,
  HttpEvent,
  HttpInterceptor
} from '@angular/common/http';
import { Observable } from 'rxjs';
import { TokenService } from '@modules/auth/services/token.service';

@Injectable()
export class InjetTokenInterceptor implements HttpInterceptor {
  token:string | null ='';
  constructor(private _tokenService:TokenService) {}

  intercept(request: HttpRequest<unknown>, next: <PERSON>ttpHandler): Observable<HttpEvent<unknown>> {
    try {
      this.token=this._tokenService.getToken();
      let newRequest=request;
      newRequest =request.clone(
        {
          setHeaders:{
            authorization:`${this.token}`
          }
        }
        )

    return next.handle(newRequest);
    } catch (error) {
      console.log("token no interceptado");
      return next.handle(request);
    }

  }
}
