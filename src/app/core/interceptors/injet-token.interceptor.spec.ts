import { HttpClientTestingModule } from '@angular/common/http/testing';
import { TestBed } from '@angular/core/testing';

import { InjetTokenInterceptor } from './injet-token.interceptor';

describe('InjetTokenInterceptor', () => {
  beforeEach(() => TestBed.configureTestingModule({
    imports:[
      HttpClientTestingModule
    ],
    providers: [
      InjetTokenInterceptor
      ]
  }));

  it('should be created', () => {
    const interceptor: InjetTokenInterceptor = TestBed.inject(InjetTokenInterceptor);
    expect(interceptor).toBeTruthy();
  });
});
