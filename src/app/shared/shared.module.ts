import { RouterModule } from '@angular/router';

import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HeaderComponent } from './components/header/header.component';
import { HeaderReportComponent } from './components/header-report/header-report.component';
import { ChangePasswordComponent } from './components/change-password/change-password.component';
import { ButtonModule } from 'primeng/button';
import { ReactiveFormsModule } from '@angular/forms';
import { MenubarModule} from 'primeng/menubar';
import { TabViewModule } from 'primeng/tabview';
import { NgbModule } from '@ng-bootstrap/ng-bootstrap';
import { FormatRutPipe } from './pipes/format-rut.pipe';
import {CalendarModule} from 'primeng/calendar';
import {DropdownModule} from 'primeng/dropdown';
import {PaginatorModule} from 'primeng/paginator';
import {InputMaskModule} from 'primeng/inputmask';
import {AutoCompleteModule} from 'primeng/autocomplete';


@NgModule({
  declarations: [
    HeaderComponent,
    HeaderReportComponent,
    ChangePasswordComponent,
    FormatRutPipe
  ],
  imports: [
    CommonModule,
    RouterModule,
    ButtonModule,
    ReactiveFormsModule,
    MenubarModule,
    TabViewModule,
    NgbModule,
    CalendarModule,
    DropdownModule,
    PaginatorModule,
    InputMaskModule,
    AutoCompleteModule
  ],
  exports:[
    HeaderComponent,
    HeaderReportComponent,
    FormatRutPipe,
    PaginatorModule,
    AutoCompleteModule

  ]
})
export class SharedModule { }
