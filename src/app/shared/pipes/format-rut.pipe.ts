import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'FormatRut'
})
export class FormatRutPipe implements PipeTransform {

  transform(rut: any, args?: any): any {
    return this.format(rut);
  }

  format(rut:any) {
    rut = this.clean(rut)

    var result = rut.slice(-4, -1) + '-' + rut.substr(rut.length - 1)
    for (var i = 4; i < rut.length; i += 3) {
      result = rut.slice(-3 - i, -i) + '.' + result
    }

    return result
  }

  clean(rut:any) {
    return typeof rut === 'string'
      ? rut.replace(/^0+|[^0-9kK]+/g, '').toUpperCase()
      : ''
  }

}
