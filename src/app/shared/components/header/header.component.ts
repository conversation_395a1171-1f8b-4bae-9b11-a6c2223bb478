import { AuthService } from '@modules/auth/services/auth.service';
import { UserService } from './../../../modules/maintenance/services/user.service';
import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import { UserDto } from '@core/models/user/user.model';
import {MenuItem} from 'primeng/api';

@Component({
  selector: 'app-header',
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.scss']
})
export class HeaderComponent implements OnInit {

  datosUsuario: any;
  nombreUsuario:any;
  user !: UserDto;
  public items: MenuItem[];
  public itemsReporte!:MenuItem[];

  constructor(
    private _userService:UserService,
    private _authService:AuthService,
    private router:Router) {
    this.items = [];
  }

  ngOnInit(): void {

    this.datosUsuario=JSON.parse(sessionStorage.getItem('userData')+"");

    this.items = [
      {label:'Home', icon:'pi pi-fw pi-home', routerLink: '/home' }
   ];
   this.itemsReporte = [
     {
         label: 'Reportes', icon: 'pi pi-fw pi-chart-bar',
         items: [
              {
                  label: 'SAC',
                  items: [{label:'Detalle',icon:'pi pi-fw pi-list',routerLink:'report/detail'},
                          {label:'Consolidado',icon:'pi pi-fw pi-th-large',routerLink:'report/consolidate'}]
              },
              {
                  label: 'Cola de Reportes',
                  items: [{label:'Ver Cola',icon:'pi pi-fw pi-cog',routerLink:'report/queue'}]
              }
      ]
     }
 ]


   //admin
   if(this.datosUsuario.profile.id == 2  ){
     this.items.push(
       this.itemsReporte[0],
       {label:'Mantención', icon:'pi pi-fw pi-cog', routerLink: 'maintenance'},
       {label:'Auditoria', icon:'pi pi-fw pi-check-square', routerLink: 'audit'}
     );
   }
   //viewer
   if(this.datosUsuario.profile.id == 7){
     this.items.push(
       this.itemsReporte[0]
     );
   }
   //maintenance
    if(this.datosUsuario.profile.id == 8){
      this.items.push(
       {label:'Mantención', icon:'pi pi-fw pi-cog', routerLink: 'maintenance'},
      );
    }//auditoria
    if(this.datosUsuario.profile.id == 9){
      this.items.push(
        {
          label: 'Cola de Reportes',
          items: [{label:'Ver Cola',icon:'pi pi-fw pi-cog',routerLink:'report/queue'}]
      },
       {label:'Auditoria', icon:'pi pi-fw pi-cog', routerLink: 'audit'},
      );
    }

  }

  logOut(){
    this._authService.logOut();
  }


}
