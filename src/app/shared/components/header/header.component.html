<p-menubar [model]="items" class="mb-5">
  <ng-template pTemplate="start" class="bg-secondary">
    <img src="assets/img/logo-mobid-1.svg" width="120" height="20" class="img-fluid mx-3" alt=""/>
  </ng-template>
  <ng-template pTemplate="end">
    <div ngbDropdown class="offset-md-3 col-3 text-end" style="padding-right:22px ;">

      <button class="btn text-light mt-1" id="menuUsuario" ngbDropdownToggle style="padding-right:80px ;">
        <div ngbDropdownMenu aria-labelledby="menuUsuario">
          <button ngbDropdownItem [routerLink]="['changepass']">Cambiar Contraseña</button>
          <button ngbDropdownItem (click)="logOut()">Cerrar Sesion</button>
        </div>
        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" class="bi bi-person-circle" viewBox="0 0 16 16">
          <path d="M11 6a3 3 0 1 1-6 0 3 3 0 0 1 6 0z" />
          <path fill-rule="evenodd"
            d="M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8zm8-7a7 7 0 0 0-5.468 11.37C3.242 11.226 4.805 10 8 10s4.757 1.225 5.468 2.37A7 7 0 0 0 8 1z" />
        </svg>
        <span class="text-dark"> {{datosUsuario.username|uppercase}}</span>
      </button>
    </div>
  </ng-template>
</p-menubar>

