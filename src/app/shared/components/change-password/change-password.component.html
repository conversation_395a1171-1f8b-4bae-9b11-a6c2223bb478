
<div class="row mt-1">
  <div class="col-12 "></div>
</div>
<div class=" col-12  col-sm-12 offset-md-8 col-md-3 mt-5">
  <form [formGroup]="changePasswordForm" (ngSubmit)="sendData()" novalidate>
      <div class="card">
          <div class="card-body">
              <h5 class="card-title text-center">Cambiar Contrase&ntilde;a</h5>
              <div class="col-12">
                  <div class="p-fluid">
                      <div class="p-field p-3">
                          <label for="txtOldPassword" class="my-2">Contrase&ntilde;a Actual</label>
                          <div class="input-group">
                              <input id="txtOldPassword" [type]="fieldTextType1 ? 'text' : 'password'"
                                  class="form-control" formControlName="OldPass">
                              <span class="input-group-text">
                                  <em class="py-1 pi"
                                      [ngClass]="{'pi-eye': !fieldTextType1,'pi-eye-slash ': fieldTextType1}"
                                      (click)="toggleFieldTextType1()"></em>
                              </span>
                          </div>
                          <div *ngIf="changePasswordForm.controls['OldPass'].invalid && (changePasswordForm.controls['OldPass'].dirty || changePasswordForm.controls['OldPass'].touched)"
                          class="error mt-1">
                          <div *ngFor="let validation of smsValidaciones.OldPass">
                              <span
                                  *ngIf="changePasswordForm.controls['OldPass'].hasError(validation.type) && (changePasswordForm.controls['OldPass'].dirty || changePasswordForm.controls['OldPass'].touched)">{{validation.message}}</span>
                          </div>
                      </div>
                      </div>
                      <div class="p-field p-3">
                          <label for="txtNewPassword" class="my-2">Nueva Contrase&ntilde;a</label>
                          <div class="input-group">
                              <input id="txtNewPassword" [type]="fieldTextType2 ? 'text' : 'password'"
                                  class="form-control" formControlName="NewPass" (blur)="validationSamePass()" (keyup)="validationSameOldPass()" />
                              <span class="input-group-text">
                                  <em class="py-1 pi"
                                      [ngClass]="{'pi-eye': !fieldTextType2,'pi-eye-slash ': fieldTextType2}"
                                      (click)="toggleFieldTextType2()" ></em>
                              </span>
                          </div>
                          <div *ngIf="changePasswordForm.controls['NewPass'].invalid && (changePasswordForm.controls['NewPass'].dirty || changePasswordForm.controls['NewPass'].touched)"
                              class="error mt-1">
                              <div *ngFor="let validation of smsValidaciones.NewPass">
                                  <span
                                      *ngIf="changePasswordForm.controls['NewPass'].hasError(validation.type) && (changePasswordForm.controls['NewPass'].dirty || changePasswordForm.controls['NewPass'].touched)">{{validation.message}}</span>
                              </div>
                          </div>
                          <div *ngIf="errorOldPass" class="error mt-1">
                              La nueva contraseña debe ser diferente a la actual
                          </div>
                      </div>
                      <div class="p-field p-3">
                          <label for="txtNewPassword2" class="my-2">Confirmar Contrase&ntilde;a</label>
                          <div class="input-group">
                              <input id="txtNewPassword2" [type]="fieldTextType3 ? 'text' : 'password'"
                                  class="form-control" formControlName="RepeatPass" (keyup)="validationSamePass()"/>
                              <span class="input-group-text">
                                  <em class="py-1 pi"
                                      [ngClass]="{'pi-eye': !fieldTextType3,'pi-eye-slash ': fieldTextType3}"
                                      (click)="toggleFieldTextType3()" ></em>
                              </span>
                          </div>
                          <div *ngIf="changePasswordForm.controls['RepeatPass'].invalid && (changePasswordForm.controls['RepeatPass'].dirty || changePasswordForm.controls['RepeatPass'].touched)"
                                  class="error mt-1">
                                  <div *ngFor="let validation of smsValidaciones.RepeatPass">
                                      <span
                                          *ngIf="changePasswordForm.controls['RepeatPass'].hasError(validation.type) && (changePasswordForm.controls['RepeatPass'].dirty || changePasswordForm.controls['RepeatPass'].touched)">{{validation.message}}</span>
                                  </div>
                              </div>
                              <div *ngIf="errorPass" class="error mt-1">
                                  Contraseñas no coinciden
                              </div>
                      </div>
                      <div class="p-field p-3">
                          <p-button label="Cambiar Contrase&ntilde;a" type="submit" [disabled]="!changePasswordForm.valid || errorPass || errorOldPass" styleClass="p-button-secondary" icon="pi pi-sign-in" iconPos="right" [loading]="loading[0]" ></p-button>
                      </div>
                  </div>
              </div>
          </div>
      </div>
</form>
</div>
