import { ChangePassword } from '@core/models/auth/changePassword.model';
import { SharedService } from './../../services/shared.service';
import { Router } from '@angular/router';
import { Component, OnInit } from '@angular/core';
import Swal from 'sweetalert2';
import { AuthService } from '@modules/auth/services/auth.service';
import { FormControl, FormGroup, Validators } from '@angular/forms';

@Component({
  selector: 'app-change-password',
  templateUrl: './change-password.component.html',
  styleUrls: ['./change-password.component.scss']
})
export class ChangePasswordComponent implements OnInit {

  changePasswordForm:FormGroup= new FormGroup({});

  passwordPattern:any='/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])/';
  smsValidaciones: any;
  nombreUsuario:any;
  datosUsuarioLogeado:any;
  fieldTextType1:boolean=false;
  fieldTextType2:boolean=false;
  fieldTextType3:boolean=false;
  loading = [false];
  errorPass: any;
  errorOldPass: any;
  cambioPassword:ChangePassword = {
    id: 0,
    username: '',
    password:'',
    oldPassword: '',
    ip:''
  }
  constructor(
    private _sharedService:SharedService,
    private _authService:AuthService,
    private router:Router
    ) {
      this.smsValidaciones = {
        OldPass: [
          { type: 'required',  message:'Se te ha olvidado ingresar la Contraseña Actual' },
          { type: 'minlength', message:'Contraseña debe tener minimo 8 caracteres'},
          { type: 'maxlength', message:'Contraseña no puede tener más de 36 caracteres' },
          { type: 'pattern',   message:'Contraseña debe tener mínimo un número, una minúscula y una mayúscula, sin caracteres especiales o espacios' }
        ],
        NewPass: [
          { type: 'required',  message:'Se te ha olvidado ingresar la nueva Contraseña' },
          { type: 'minlength', message:'Nueva Contraseña debe tener minimo 8 caracteres'},
          { type: 'maxlength', message:'Nueva Contraseña no puede tener más de 36 caracteres' },
          { type: 'pattern',   message:'Nueva Contraseña debe tener mínimo un número, una minúscula y una mayúscula, sin caracteres especiales o espacios' }
        ],
        RepeatPass: [
          { type: 'required',  message:'Debes confirmar la nueva Contraseña' },
          { type: 'minlength', message:'Confirmar Contraseña debe tener minimo 8 caracteres'},
          { type: 'maxlength', message:'Confirmar Constraseña no puede tener más de 36 caracteres' },
          { type: 'pattern',   message:'Confirmar Contraseña debe tener mínimo un número, una minúscula y una mayúscula, sin caracteres especiales o espacios' }
        ]
      };

      this.changePasswordForm = new FormGroup({
        OldPass:new FormControl('',[Validators.required,Validators.minLength(8),Validators.maxLength(36),Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*?[0-9a-zA-Z]+$)/)]),
        NewPass:new FormControl('',[Validators.required,Validators.minLength(8),Validators.maxLength(36),Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*?[0-9a-zA-Z]+$)/)]),
        RepeatPass:new FormControl('',[Validators.required,Validators.minLength(8),Validators.maxLength(36),Validators.pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])(?=.*?[0-9a-zA-Z]+$)/)])
        });
      this.errorPass = false;
      this.errorOldPass = false;
    }

  ngOnInit(): void {
    // TODO document why this method 'ngOnInit' is empty

  }

  sendData(){
    this.changePassword();
  }

  //corregir obtencion de datos usuario logeado
  //corregir obtencion datos ip
  changePassword(){
    let ip=sessionStorage.getItem('ip')+"";

    const {id,username}=JSON.parse( sessionStorage.getItem('userData')+"");
    const {NewPass,OldPass}=this.changePasswordForm.value;

      this.cambioPassword.id=id;
      this.cambioPassword.ip=ip;
      this.cambioPassword.username = username;
      this.cambioPassword.oldPassword=OldPass;
      this.cambioPassword.password=NewPass;

    let observable = this._authService.changePassword(this.cambioPassword);
    observable.subscribe(
      (data: any) => {
        if(data.code==0){
          Swal.fire('Peticion exitosa', data.message, 'success');
          this._authService.logOut();
        }else{
          Swal.fire('Ha ocurrido algo', data.message, 'error');
        }
        //this.router.navigate(['/home/<USER>']);
        return 0;
      },
      (errorData)=>{
        Swal.fire('Error al Cambiar la contraseña', errorData.message, 'error');
        this.loading[0] = false;
      },
      () => {
        this.loading[0] = false;
      }
    );
  }

  toggleFieldTextType1() {
    this.fieldTextType1 = !this.fieldTextType1;
  }

  toggleFieldTextType2() {
    this.fieldTextType2 = !this.fieldTextType2;
  }
  toggleFieldTextType3() {
    this.fieldTextType3 = !this.fieldTextType3;
  }

  validationSamePass() {
    this.errorPass = this.changePasswordForm.value.NewPass != this.changePasswordForm.value.RepeatPass;
  }

  validationSameOldPass() {
    this.errorOldPass = this.changePasswordForm.value.OldPass  ==  this.changePasswordForm.value.NewPass;
  }

}
