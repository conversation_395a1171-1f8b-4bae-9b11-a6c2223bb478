import { HeaderReportService } from '@shared/services/header-report.service';
import { Component, EventEmitter, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { FilterDto } from '@core/models/report/filterDto.model';
import { QueueDto } from '@core/models/report/queueDto.model';
import { ReportDto } from '@core/models/report/reportDto.model';
import { ComboDto } from '@core/models/shared/comboDto.model';
import { ResponseComboDto } from '@core/models/shared/responseComboDto.model';
import { UserDto } from '@core/models/user/user.model';
import { PrimeNGConfig } from 'primeng/api';
import Swal from 'sweetalert2';
import { ResponseListUserDto } from '@core/models/report/responseListUserDto.model';
import { SharedService } from '@shared/services/shared.service';
import { UserService } from '@modules/maintenance/services/user.service';
import { ComboUserDto } from '@core/models/report/comboUserDto.model';

@Component({
  selector: 'app-header-report',
  templateUrl: './header-report.component.html',
  styleUrls: ['./header-report.component.scss']
})
export class HeaderReportComponent implements OnInit {

  txtDateFrom: Date = new Date();
  txtDateTo: Date = new Date();

  txtMessageDataView: string = "";
  txtMessage: string = "";

  headerData: string[] = [];
  bodyData: QueueDto[] = [];

  statusListQueue: ComboDto[] = [];
  selectedStatusQueue: number = 0;

  statusListDetail: ComboDto[] = [];
  selectedStatusDetail: number = 0;

  companyList: ComboDto[] = [];
  selectedCompany: number = 0;

  moduleList: ComboDto[] = [];

  userList: ComboUserDto[] = [];
  selectedUser: number = 0;

  totalRecords: number = 0;
  rowsPage: number = 10;
  page: number = 0;

  smsValidaciones: any;
  fechaValidas: boolean = false;
  defaultDate: any;
  defaultDate2: any;
  today: any
  ip:any;
  tipoReporteConsolidado:any;
  perfil:any;

  @Output() DatosReporte = new EventEmitter();
  reporteSeleccionado: any;
  txtPhoneNumber: any;

  constructor(
    private _sharedService:SharedService,
    private _headerReportService:HeaderReportService,
    private _userService:UserService,
    private config: PrimeNGConfig
  ) {
    this.obtainDataCboStatusQueue();
    this.obtainDataCboStatusDetail();
    this.obtainDataCboUser(0);
    this.obtainDataCboModule();
   }

  ngOnInit(): void {
    this.perfil = JSON.parse( sessionStorage.getItem("userData")+"");

    let today = new Date();

    this.today=today.getMonth()+1 + '/' + today.getDate() + '/' + today.getFullYear();
     this.defaultDate  = new Date(this.today);
    this.defaultDate2  = new Date(this.today);

    this.config.setTranslation({
      "today":'Hoy',
      "clear": "Limpiar",
      "dayNames": ["Domingo", "Lunes", "Martes", "Miercoles", "Jueves", "Viernes", "Sabado"],
      "dayNamesShort": ["Dom", "Lun", "Mar", "Mie", "Jue", "Vie", "Sab"],
      "dayNamesMin": ["Do","Lu","Ma","Mi","Ju","Vi","Sa"],
      "monthNames": ["Enero","Febrero","Marzo","Abril","Mayo","Junio","Julio","Agosto","Septiembre","Octubre","Noviembre","Diciembre"],
      "monthNamesShort": ["Ene", "Feb", "Mar", "Abr", "May", "Jun","Jul", "Ago", "Sep", "Oct", "Nov", "Dic"],
    });
    this._headerReportService.reporteSeleccionado$.subscribe(
      (data: any) => {
        this.reporteSeleccionado = data;
      }
    );

    this._headerReportService.fechaSeleccionada$.subscribe(
      (data:any)=>{
        this.cleanDates();
      }
    );
  }
  reportForm = new FormGroup({
    txtDateFrom: new FormControl('', [Validators.required]),
    txtDateTo: new FormControl('', [Validators.required,]),
    selectedStatusQueue: new FormControl(''),
    selectedStatusDetail: new FormControl(''),
    selectedUser: new FormControl(''),
    txtPhoneNumber: new FormControl(''),
    selectedModule:new FormControl(''),
  });

  sendData() {

    let resultDate = this.compareDate();
    this.txtPhoneNumber = this.cleanPhoneNumber(this.reportForm.controls['txtPhoneNumber'].value);
    if (resultDate==1) {
      Swal.fire("Ha ocurrido algo", "Fecha inicio debe ser menor que Fecha Termino", "error");
    }else if (resultDate==3 && this.reporteSeleccionado=="consolidado" || this.reporteSeleccionado=="audit" ) {
      Swal.fire("Ha ocurrido algo", "El rango de fechas entre la Fecha Inicio y Termino no debe superar los 31 días", "error");
        return
    }else if(resultDate==2) {
      Swal.fire("Ha ocurrido algo", "Fecha Inicio no debe ser mayor a 6 meses de la fecha actual", "error");
          return
    }else if (resultDate ==4) {
      Swal.fire("Ha ocurrido algo", "Fecha Termino no debe superar el dia actual", "error");
      return
    }else if (this.txtPhoneNumber == "" && this.reporteSeleccionado=="detalle") {
      Swal.fire("Ha ocurrido algo", "Numero de teléfono es necesario", "error");
    } else {
      let reportDto: ReportDto = this.fillReportDto();
       this._headerReportService.datosReporte$.emit(reportDto);
       this._headerReportService.tipoReporteConsolidado$.emit(this.tipoReporteConsolidado);
    }

  }
  tipoReporteConsolidadoMetodo(event:any){
    this.tipoReporteConsolidado=event;
  }

  cleanDates(){
    this.defaultDate  = new Date(this.today);
    this.defaultDate2  = new Date(this.today);
  }

  private cleanPhoneNumber(phoneNumber: string) {
    return phoneNumber.replace(/[+() -]/g, "");
  }

  compareDate() {
    let desde = this.reportForm.controls['txtDateFrom'].value;
    let hasta = this.reportForm.controls['txtDateTo'].value;


    if (desde > hasta) {
      return 1;
    }else if (this.restarFechas(new Date(desde),new Date(this.today))>180){
      return 2; //fecha mayor a 6 meses con la fecha actual
    }else if (this.restarFechas(new Date(desde),new Date(hasta))>33 ){
      return 3; //Fecha mayor de 31 dias entre campo desde y hasta
    }else if (new Date(hasta) > new Date(this.today)){
      return 4;
    } else {
      return 5;
    }
  }

  restarFechas(inicial :Date,final:Date){

    let day1 = inicial.getTime();
    let day2 = final.getTime();

    let difference= day1-day2;
    let days = difference/(1000 * 3600 * 24);

    return Math.abs(days);
  }


  private parseDateFromServer(date: Date) {
    let parseDate: string = "";
    let year = date.getFullYear();
    let monthNumber = date.getMonth() + 1;
    let monthText = this.addCharacter(monthNumber.toString(), "0", 2);
    let dayNumber = date.getDate();
    let dayText = this.addCharacter(dayNumber.toString(), "0", 2);
    parseDate = year + '-' + monthText + '-' + dayText;
    return parseDate;
  }

  private addCharacter(text: string, character: string, size: number) {
    for (let index = text.length; index < size; index++) {
      text = character + text;
    }
    return text;
  }

  obtainDataCboStatusQueue() {
    let observable = this._sharedService.getDataListStatusQueueReport();
    observable.subscribe(
      (obj: ResponseComboDto) => {
        let list: ComboDto[] = obj.data;
        this.statusListQueue = list;
        let cbo: ComboDto = new ComboDto();
        cbo.id = 0;
        cbo.description = "Seleccione";
        this.statusListQueue.unshift(cbo);
      },
      (errorData) => {
        this.txtMessage = errorData.error;
        console.log(errorData);
      }
    );
  }
  obtainDataCboStatusDetail() {
    let observable = this._sharedService.getDataListStatus();
    observable.subscribe(
      (obj: ResponseComboDto) => {
        let list: ComboDto[] = obj.data;
        this.statusListDetail = list;
        let cbo: ComboDto = new ComboDto();
        cbo.id = 0;
        cbo.description = "Seleccione";
        this.statusListDetail.unshift(cbo);
      },
      (errorData) => {
        this.txtMessage = errorData.error;
        console.log(errorData);
      }
    );
  }

  obtainDataCboModule() {
    let observable = this._sharedService.getDataListModuleAudit();
    observable.subscribe(
      (obj: ResponseComboDto) => {
        this.moduleList = obj.data;
        let cbo: ComboDto = new ComboDto();
        cbo.id = 0;
        cbo.description = "Seleccione";
        this.moduleList.unshift(cbo);

      },
      (errorData) => {
        this.txtMessage = errorData.error;
        console.log(errorData);
      }
    );
  }

  obtainDataCboCompany() {
    let observable = this._sharedService.getDataListCompany();
    observable.subscribe(
      (obj: ResponseComboDto) => {
        this.companyList = obj.data;
        let cbo: ComboDto = new ComboDto();
        cbo.id = 0;
        cbo.description = "Seleccione";
        this.companyList.unshift(cbo);

      },
      (errorData) => {
        this.txtMessage = errorData.error;
        console.log(errorData);
      }
    );
  }

  obtainDataCboUser(idCompany: number) {
    let observable = this._userService.getDataListUser(idCompany);
    observable.subscribe(
      (obj: ResponseListUserDto) => {
        this.userList = obj.data;
        let user: ComboUserDto = new ComboUserDto();
        user.id = 0;
        user.username = "Seleccione";
        this.userList.unshift(user);
      },
      (err:any) => {
        this.txtMessage = err.error;
        console.log(err);
      }
    );
  }

  private fillReportDto() {
    let dateFrom = this.parseDateFromServer(this.reportForm.controls['txtDateFrom'].value);
    let dateTo = this.parseDateFromServer(this.reportForm.controls['txtDateTo'].value);
    console.log(dateFrom < dateTo);

    let filters: FilterDto = new FilterDto();
    filters.dateFrom = dateFrom + " 00:00:00";
    filters.dateTo = dateTo + " 23:59:59";
    filters.idInputMode = 0;
    filters.idStatus = this.reportForm.controls[this.reporteSeleccionado == 'cola'?'selectedStatusQueue':'selectedStatusDetail'].value;
    filters.idCompany = this.selectedCompany;
    filters.idAccount = this.reportForm.controls['selectedUser'].value
    filters.numberMobil = this.cleanPhoneNumber(this.reportForm.controls['txtPhoneNumber'].value);
    filters.idModule=this.reportForm.controls['selectedModule'].value;
    let reportDto: ReportDto = new ReportDto();
    reportDto.page = this.page;
    reportDto.size = this.rowsPage;
    reportDto.ip=this.ip == null ? sessionStorage.getItem('ip'):this.ip;
    reportDto.filters = filters;
    return reportDto;
  }

}


