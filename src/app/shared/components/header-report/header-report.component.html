<div class="card">
  <h5 *ngIf="reporteSeleccionado=='consolidado'">Reporte SAC Consolidado</h5>
  <h5 *ngIf="reporteSeleccionado=='detalle'">Reporte SAC Detalle</h5>
  <h5 *ngIf="reporteSeleccionado=='cola'">Cola de Reportes</h5>
  <h5 *ngIf="reporteSeleccionado!='cola' && reporteSeleccionado!='detalle'&& reporteSeleccionado!='consolidado'">Reporte Auditoria</h5>
  <em class="mt-2"></em>
  <form class="row my-3" [formGroup]="reportForm" (ngSubmit)="sendData()">
      <div class="col-12 col-md-2">
          <span class="p-float-label">
              <p-calendar inputId="dateFrom" [(ngModel)]="defaultDate" formControlName="txtDateFrom"
                  [monthNavigator]="true" [yearNavigator]="true" yearRange="2017:2025" dateFormat="dd-mm-yy"
                  [showIcon]="true" [showButtonBar]="true"></p-calendar>
              <label for="DateFrom">Fecha Inicio</label>
          </span>
      </div>
      <div class="col-12 col-md-2">
          <span class="p-float-label">
              <p-calendar inputId="dateTo" [(ngModel)]="defaultDate2" formControlName="txtDateTo"
                  [monthNavigator]="true" [yearNavigator]="true" yearRange="2017:2025" dateFormat="dd-mm-yy"
                  [showIcon]="true" [showButtonBar]="true" class="col-12"></p-calendar>
              <label for="dateTo">Fecha Termino</label>
          </span>
      </div>
      <div *ngIf="reporteSeleccionado=='cola'" class="col-12 col-md-2">

              <span class="p-float-label">
                  <p-dropdown inputId="txtStatusQueue" [autoDisplayFirst]="true" [options]="statusListQueue"
                      formControlName="selectedStatusQueue" optionLabel="description" optionValue="id"></p-dropdown>
                  <label for="txtStatusQueue">Estado</label>
              </span>
      </div>
      <div *ngIf="reporteSeleccionado=='detalle'" class="col-12 col-md-2">

          <span class="p-float-label">
              <p-dropdown inputId="txtStatusDetail" [autoDisplayFirst]="true" [options]="statusListDetail"
                  formControlName="selectedStatusDetail" optionLabel="description" optionValue="id"></p-dropdown>
              <label for="txtStatusDetail">Estado</label>
          </span>
      </div>
      <div *ngIf="reporteSeleccionado !='detalle' && reporteSeleccionado !='consolidado' && reporteSeleccionado !='cola' && (perfil.profile.id == 2 || perfil.profile.id==9 )" class="col-12 col-md-2">
          <span class="p-float-label">
              <p-dropdown inputId="txtModule" [autoDisplayFirst]="true" [options]="moduleList"
                  formControlName="selectedModule" optionLabel="description" optionValue="id"></p-dropdown>
              <label for="txtModule">Accion</label>
          </span>
  </div>

          <div *ngIf="reporteSeleccionado !='detalle' && reporteSeleccionado !='consolidado' && (perfil.profile.id == 2 || perfil.profile.id==9 || perfil.profile.id==7)" class="col-12 col-md-2">
              <span class="p-float-label">
                  <p-dropdown inputId="cboUser" [autoDisplayFirst]="true" [options]="userList"
                      formControlName="selectedUser" optionLabel="username" optionValue="id"></p-dropdown>
                  <label for="cboUser">Usuario</label>
              </span>
      </div>

      <div *ngIf="reporteSeleccionado=='detalle'" class="col-12 col-md-2">
          <span class="p-float-label">
              <p-inputMask inputId="phoneNumber"  mask="+(56) 9 9999-99-99" formControlName="txtPhoneNumber">
              </p-inputMask>
              <label for="phoneNumber">N&uacute;mero Tel&eacute;fono</label>
          </span>
      </div>
      <div class="col-12 col-md-2 ">
          <button type="submit" [disabled]="!reportForm.valid" class="btn btn-primary"
              (click)="tipoReporteConsolidadoMetodo(1)">Buscar</button>
      </div>
      <div *ngIf="reporteSeleccionado=='consolidado'" class="col-12 col-md-3 ">
          <button type="submit" [disabled]="!reportForm.valid" class="btn btn-primary"
              (click)="tipoReporteConsolidadoMetodo(2)">Generar Reporte Consolidado</button>
      </div>
      <div *ngIf="reporteSeleccionado=='consolidado'" class="col-12 col-md-3">
          <button type="submit" [disabled]="!reportForm.valid" class="btn btn-primary"
              (click)="tipoReporteConsolidadoMetodo(3)">Generar Reporte Detalle Completo</button>
      </div>
  </form>
</div>
