import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { ResponseComboDto } from '@core/models/shared/responseComboDto.model';
import { TokenService } from '@modules/auth/services/token.service';
import { Observable} from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class SharedService {
   private readonly URL =environment.urlAccountService;
  private readonly URLGENERAL =environment.urlGeneralService;

  ipLocal:any;
  valor:any;

  constructor(
    private http:HttpClient,
    private _tokenService:TokenService
  ) { }

  getDataListStatus():Observable<ResponseComboDto>{
    const TOKEN = this._tokenService.getToken()+"";
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.get<ResponseComboDto>(`${this.URLGENERAL}/api/list/status/report`,options);
  }

  getDataListStatusQueueReport():Observable<ResponseComboDto>{
    const TOKEN = this._tokenService.getToken()+"";
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.get<ResponseComboDto>(`${this.URLGENERAL}/api/list/status/queueReport`,options);
  }

  getDataListInputMode():Observable<ResponseComboDto>{
    const TOKEN = this._tokenService.getToken()+"";
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.get<ResponseComboDto>(`${this.URLGENERAL}/api/list/inputMode`,options);
  }

  getDataListCompany():Observable<ResponseComboDto>{
    const TOKEN = this._tokenService.getToken()+"";
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.get<ResponseComboDto>(`${this.URLGENERAL}/api/list/company`,options);
  }

  public getDataListModuleAudit():Observable<ResponseComboDto>{
    const TOKEN = this._tokenService.getToken()+"";
    let headers = new HttpHeaders({
      'Content-Type': 'application/json',
      'Authorization':TOKEN});
    let options = { headers: headers };
    return this.http.get<ResponseComboDto>(`${this.URLGENERAL}/api/list/activity`,options);
  }

  validarCaracterEspecial(campo:string){
    let letra
    let valor
    for (const element of campo) {
         letra  = element.toUpperCase().charCodeAt(0);

        if(letra >64 && letra < 91){
           valor= false;

        }else if(letra >47 && letra< 58){
          valor= false;
        }else{
          valor = true;
          break;
        }
    }
    return valor
  }

  ConvertToCSV(objArray:any, headerList:any,headerSpanish:any):string {
    let array = typeof objArray != 'object' ? JSON.parse(objArray) : objArray;
    let str = '';
    let row = '#;';
    for (let index in headerList) {
     row += headerSpanish[index] + ';';
    }
    row = row.slice(0, -1);
    str += row + '\r\n';
    for (let i = 0; i < array.length; i++) {
     let line = (i+1)+'';
     for (let index in headerList) {
      let head = headerList[index];
      if(head=="enabled"){
        if(array[i][head]){
          line += ';' + 'Activo';
        }else{
          line += ';' + 'Inactivo';
        }

      }else{
      line += ';' + array[i][head];
       }
     }
     str += line + '\r\n';
    }
    return str.toUpperCase();
   }

   onlyWordsNumberSpace(campo: string) {
    let letra;
    let valor;

    if (campo != null) {
      for (const element of campo) {
        letra = element.toUpperCase().charCodeAt(0);

        if (letra > 64 && letra < 91) {
          valor = false;

        } else if (letra > 47 && letra < 58) {
          valor = false;
        } else if (letra == 32) {
          valor = false;
        } else {
          valor = true;
          break;
        }
      }
    } else {
      valor = false;
    }
    return valor
  }

  onlyWordsNumbers(campo: string) {
    let letra
    let valor
    if (campo != null) {
      for (const element of campo) {
        letra = element.toUpperCase().charCodeAt(0);
        if (letra > 64 && letra < 91) {
          valor = false;
        } else if (letra > 47 && letra < 58) {
          valor = false;
        } else {
          valor = true;
          break;
        }
      }
    } else {
      valor = false;
    }

    return valor
  }

  onlyNumbers(campo: string) {
    let letra
    let valor
    if (campo != null) {
      for (const element of campo) {
        letra = element.toUpperCase().charCodeAt(0);
       if(letra > 47 && letra < 58) {
          valor = false;
        } else {
          valor = true;
          break;
        }
      }
    } else {
      valor = false;
    }

    return valor
  }

  checkRut(rut:any) {

    // Obtiene el valor ingresado quitando puntos y guión.
   this.valor = this.clean(rut);

   // Divide el valor ingresado en dígito verificador y resto del RUT.
   let cuerpo = this.valor.slice(0, -1);
   let dv = this.valor.slice(-1).toUpperCase();

   // Separa con un Guión el cuerpo del dígito verificador.
   rut = this.format(rut);

   // Si no cumple con el mínimo ej. (n.nnn.nnn)
   if (cuerpo.length < 7) {

     //'Ingresó un RUT muy corto, el RUT debe ser mayor a 7 Dígitos. Ej: x.xxx.xxx-x';
     return true;
   }

   // Calcular Dígito Verificador "Método del Módulo 11"
  let suma = 0;
  let multiplo = 2;

   // Para cada dígito del Cuerpo
   for (let i = 1; i <= cuerpo.length; i++) {
     // Obtener su Producto con el Múltiplo Correspondiente
     let index = multiplo * this.valor.charAt(cuerpo.length - i);

     // Sumar al Contador General
     suma = suma + index;

     // Consolidar Múltiplo dentro del rango [2,7]
     if (multiplo < 7) {
       multiplo = multiplo + 1;
     } else {
       multiplo = 2;
     }
   }

   // Calcular Dígito Verificador en base al Módulo 11
   let dvEsperado = (11 - (suma % 11))+"";

   // Casos Especiales (0 y K)
   dv = dv == "K" ? 10 : dv;
   dv = dv == 0 ? 11 : dv;

   // Validar que el Cuerpo coincide con su Dígito Verificador
   if (dvEsperado != dv) {
 //' Es <strong>INCORRECTO</strong>.';

     return true;
   } else {
     //CORRECTO</strong>.';
     return false;
   }
 }

 clean(rut:any) {
  return typeof rut === 'string'
    ? rut.replace(/^0+|[^0-9kK]+/g, '').toUpperCase()
    : ''
}

format(rut:any) {
  rut = this.clean(rut)
  let result = rut.slice(-4, -1) + '-' + rut.substr(rut.length - 1)
  for (let i = 4; i < rut.length; i += 3) {
    result = rut.slice(-3 - i, -i) + '.' + result
  }
  return result
}

  getIPLocal(): Observable<any> {
    return this.http.get('https://api.ipify.org?format=json');
  }
}
