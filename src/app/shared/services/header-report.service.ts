import { EventEmitter, Injectable } from '@angular/core';
import { ReportDto } from '@core/models/report/reportDto.model';

@Injectable({
  providedIn: 'root'
})
export class HeaderReportService {
  datosReporte$= new EventEmitter<ReportDto>();
  reporteSeleccionado$=new EventEmitter();
  fechaSeleccionada$=new EventEmitter();
  tipoReporteConsolidado$=new EventEmitter();
  constructor() { /* TODO document why this constructor is empty */  }
}
